buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

gradle.beforeProject { project ->
    if (project.name.contains("captcha_plugin_flutter")) {
        project.ext.set("namespace", "com.captcha.flutter")
    }
}

subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.application") ||
                project.plugins.hasPlugin("com.android.library")) {
            project.android {
                compileSdkVersion 36

                if (namespace == null) {
                    namespace project.group
                }
            }
        }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
