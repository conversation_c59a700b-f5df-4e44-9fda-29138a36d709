#!/bin/bash
# Script to generate Firebase configuration files for different flavors/environments
# Usage: ./scripts/firebase_configure.sh [flavor_name]
# Example: ./scripts/firebase_configure.sh gp

set -e

if [[ $# -eq 0 ]]; then
  echo "Error: No flavor specified."
  echo "Usage: $0 [flavor_name]"
  echo "Available flavors: pre, gp, rsyp, yhxt, tempa, bszb, dyzb, xyzq"
  exit 1
fi

# Create firebase-options directory if it doesn't exist
mkdir -p lib/generated/firebase-options

case $1 in
  pre)
    echo "Configuring Firebase for PRE flavor..."
    flutterfire configure \
      --project=gp-stock-pre \
      --out=lib/generated/firebase-options/firebase_options_pre.dart \
      --ios-bundle-id=com.gp.pre.now \
      --ios-out=ios/Flavor/pre/GoogleService-Info.plist \
      --android-package-name=com.gp.pre.stock \
      --android-out=android/app/src/pre/google-services.json
    ;;
  gp)
    echo "Configuring Firebase for GP flavor..."
    flutterfire configure \
      --project=gp-stock-main \
      --out=lib/generated/firebase-options/firebase_options_gp.dart \
      --ios-bundle-id=com.gp.now \
      --ios-out=ios/Flavor/gp/GoogleService-Info.plist \
      --android-package-name=com.gp.stock \
      --android-out=android/app/src/gp/google-services.json
    ;;
  rsyp)
    echo "Configuring Firebase for RSYP flavor..."
    flutterfire configure \
      --project=gp-stock-rsyp \
      --out=lib/generated/firebase-options/firebase_options_rsyp.dart \
      --ios-bundle-id=com.rsyp.stock.now \
      --ios-out=ios/Flavor/rsyp/GoogleService-Info.plist \
      --android-package-name=com.rsyp.stock \
      --android-out=android/app/src/rsyp/google-services.json
    ;;
  yhxt)
    echo "Configuring Firebase for YHXT flavor..."
    flutterfire configure \
      --project=gp-stock-yhxt \
      --out=lib/generated/firebase-options/firebase_options_yhxt.dart \
      --ios-bundle-id=com.yhxt.stock.now \
      --ios-out=ios/Flavor/yhxt/GoogleService-Info.plist \
      --android-package-name=com.yhxt.stock \
      --android-out=android/app/src/yhxt/google-services.json
    ;;
  tempa)
    echo "Configuring Firebase for TEMPA flavor..."
    flutterfire configure \
      --project=gp-stock-tempa \
      --out=lib/generated/firebase-options/firebase_options_tempa.dart \
      --ios-bundle-id=com.tempa.stock.now \
      --ios-out=ios/Flavor/tempa/GoogleService-Info.plist \
      --android-package-name=com.tempa.stock \
      --android-out=android/app/src/tempa/google-services.json
    ;;
  bszb)
    echo "Configuring Firebase for BSZB flavor..."
    flutterfire configure \
      --project=gp-stock-bszb \
      --out=lib/generated/firebase-options/firebase_options_bszb.dart \
      --ios-bundle-id=com.bszb.stock.now \
      --ios-out=ios/Flavor/bszb/GoogleService-Info.plist \
      --android-package-name=com.bszb.stock.now \
      --android-out=android/app/src/bszb/google-services.json
    ;;
  dyzb)
    echo "Configuring Firebase for DYZB flavor..."
    flutterfire configure \
      --project=gp-stock-dyzb \
      --out=lib/generated/firebase-options/firebase_options_dyzb.dart \
      --ios-bundle-id=com.dyzb.stock.now \
      --ios-out=ios/Flavor/dyzb/GoogleService-Info.plist \
      --android-package-name=com.dyzb.stock \
      --android-out=android/app/src/dyzb/google-services.json
    ;;
  xyzq)
    echo "Configuring Firebase for XYZQ flavor..."
    flutterfire configure \
      --project=gp-stock-xyzq \
      --out=lib/generated/firebase-options/firebase_options_xyzq.dart \
      --ios-bundle-id=com.xyzq.stock.now \
      --ios-out=ios/Flavor/xyzq/GoogleService-Info.plist \
      --android-package-name=com.xyzq.stock \
      --android-out=android/app/src/xyzq/google-services.json
    ;;
  all)
    echo "Configuring Firebase for all flavors..."
    for flavor in pre gp rsyp yhxt tempa bszb dyzb xyzq; do
      echo "Configuring $flavor..."
      $0 $flavor
    done
    ;;
  *)
    echo "Error: Invalid flavor specified."
    echo "Available flavors: pre, gp, rsyp, yhxt, tempa, bszb, dyzb, xyzq, all"
    exit 1
    ;;
esac

echo "Firebase configuration completed for flavor: $1"
