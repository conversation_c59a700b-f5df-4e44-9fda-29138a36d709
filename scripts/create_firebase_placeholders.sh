#!/bin/bash
# <PERSON>ript to create placeholder Firebase options files for all flavors
# This prevents import errors before running the actual Firebase configuration

set -e

# Create firebase-options directory if it doesn't exist
mkdir -p lib/generated/firebase-options

# Define flavors and their corresponding bundle IDs
declare -A flavors=(
    ["rsyp"]="com.rsyp.stock.now"
    ["yhxt"]="com.yhxt.stock.now"
    ["tempa"]="com.tempa.stock.now"
    ["bszb"]="com.bszb.stock.now"
    ["dyzb"]="com.dyzb.stock.now"
    ["xyzq"]="com.xyzq.stock.now"
)

# Create placeholder files for remaining flavors
for flavor in "${!flavors[@]}"; do
    bundle_id="${flavors[$flavor]}"
    
    cat > "lib/generated/firebase-options/firebase_options_${flavor}.dart" << EOF
// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'placeholder-api-key',
    appId: 'placeholder-app-id',
    messagingSenderId: 'placeholder-sender-id',
    projectId: 'placeholder-project-id',
    storageBucket: 'placeholder-project-id.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'placeholder-api-key',
    appId: 'placeholder-app-id',
    messagingSenderId: 'placeholder-sender-id',
    projectId: 'placeholder-project-id',
    storageBucket: 'placeholder-project-id.appspot.com',
    iosBundleId: '$bundle_id',
  );
}
EOF

    echo "Created placeholder for $flavor flavor"
done

echo "All Firebase placeholder files created successfully!"
echo "Run './scripts/firebase_configure.sh all' to replace with actual Firebase configuration."
