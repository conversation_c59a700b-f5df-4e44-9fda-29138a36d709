
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/open_install_manager/open_install_manager.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:video_player/video_player.dart';

import '../../../shared/constants/assets.dart';
import '../../../shared/routes/app_router.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// A splash screen that displays an intro video or logo
/// and automatically navigates to the appropriate screen based on auth state.
///
/// Features:
/// - Plays intro video with fallback to static logo
/// - Handles app lifecycle for proper video playback
/// - Includes failsafe timer to ensure navigation happens
/// - Manages system UI for immersive experience
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with WidgetsBindingObserver {
  // Video player state
  VideoPlayerController? _controller;
  bool _isVideoError = false;
  bool _isNavigating = false;
  bool _isVideoInitialized = false;
  bool _isLoading = true;

  // Animation state
  final _fadeAnimationDuration = const Duration(milliseconds: 500);
  final _logoScaleController = ValueNotifier<double>(0.95);

  // Constants
  static const _failsafeTimeoutSeconds = 5;
  static const _navigationDelayMs = 200;
  static const _logoWidth = 180.0;

  @override
  void initState() {
    super.initState();
    getIt<UserCubit>().initializeLocale();
    WidgetsBinding.instance.addObserver(this);
    _configureSystemUI();
    _initializeSplash();
    OpenInstallManager.instance.initPlatformState();
  }

  /// Configure system UI for immersive splash experience
  void _configureSystemUI() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// Initialize splash screen content and start failsafe timer
  Future<void> _initializeSplash() async {

    if (AppConfig.instance.showSplashVideo == false) {

      _navigateToNextScreen();
      return;
    }

    LogD('Initializing splash screen');
    _startFailsafeTimer();

    try {
      // Start logo pulsing animation while video loads
      // _startLogoPulseAnimation();

      // Initialize video player
      _controller = VideoPlayerController.asset(Assets.introVideo);
      await _controller!.initialize();

      if (!mounted) return;

      _controller!
        ..setLooping(false)
        ..setVolume(1.0);

      setState(() {
        _isVideoInitialized = true;
        _isLoading = false;
      });

      await _controller!.play();
      _controller!.addListener(_checkVideoCompletion);

      LogD('Video playback started');
    } catch (error) {
      LogE('Error initializing splash video: $error');
      if (mounted) {
        setState(() {
          _isVideoError = true;
          _isLoading = false;
        });
        _navigateToNextScreen();
      }
    }
  }

  /// Start a failsafe timer to ensure navigation happens even if video fails
  void _startFailsafeTimer() {
    Future.delayed(Duration(seconds: _failsafeTimeoutSeconds), () {
      if (mounted && !_isNavigating) {
        LogD('Failsafe timer triggered - forcing navigation');
        if (!_isVideoInitialized) {
          setState(() => _isVideoError = true);
        }
        _navigateToNextScreen();
      }
    });
  }

  /// Check if video has completed playback
  void _checkVideoCompletion() {
    if (_controller != null &&
        _controller!.value.isInitialized &&
        _controller!.value.position >= _controller!.value.duration) {
      LogD('Video playback completed');
      _navigateToNextScreen();
    }
  }

  /// Navigate to the appropriate screen based on authentication state
  Future<void> _navigateToNextScreen() async {
    if (_isNavigating || !mounted) return;
    _isNavigating = true;

    LogD('Preparing to navigate from splash screen');
    await _cleanupVideoController();

    final currentContext = context;
    await Future.delayed(Duration(milliseconds: _navigationDelayMs));
    if (!currentContext.mounted) return;

    // Initialize sign-in state regardless of auth status
    currentContext.read<SignInCubit>().init();

    // Navigate to main screen in both cases
    getIt<NavigatorService>().pushReplace(AppRouter.routeMain, clearStack: true);
    if (OpenInstallManager.instance.needGoSignUp) {
      getIt<NavigatorService>().push(AppRouter.routeLogin, arguments: {"isSignUp": true});
    }
  }

  /// Clean up video controller resources
  Future<void> _cleanupVideoController() async {
    if (_controller != null) {
      LogD('Cleaning up video controller');
      _controller!.removeListener(_checkVideoCompletion);
      await _controller!.pause();
      await _controller!.dispose();
      _controller = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: PopScope(
        canPop: false,
        child: Stack(
          fit: StackFit.expand,
          children: [

            if (AppConfig.instance.showSplashVideo)... [

              // Background gradient for visual appeal
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.white,
                      context.theme.primaryColor.withValues(alpha: 0.05),
                    ],
                  ),
                ),
              ),

              // Main content with animated transition
              Center(
                child: AnimatedSwitcher(
                  duration: _fadeAnimationDuration,
                  child: _buildContent(),
                ),
              ),
            ] else ...[
              /// 显示启动图
              Image.asset("assets/images/launch_screen/launch_screen.png", fit: BoxFit.contain),
            ],

            // Loading indicator
            if (_isLoading)
              Positioned(
                bottom: 50.gw,
                left: 0,
                right: 0,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: 20.gw),
                      SizedBox(
                        width: 24.gw,
                        height: 24.gw,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.0,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            context.theme.primaryColor.withValues(alpha: 0.7),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build the main content based on current state
  Widget _buildContent() {
    // Show loading placeholder
    if (!_isVideoInitialized && !_isVideoError) {
      return _buildLogoWithAnimation();
    }

    // Show static logo on error
    if (_isVideoError) {
      return _buildLogoWithAnimation();
    }

    // Show video player when ready
    return _controller != null && _controller!.value.isInitialized
        ? Container(
            key: const ValueKey('video'),
            color: Colors.white,
            child: AspectRatio(
              aspectRatio: _controller!.value.aspectRatio,
              child: VideoPlayer(_controller!),
            ),
          )
        : const SizedBox.shrink();
  }

  /// Build logo with scale animation
  Widget _buildLogoWithAnimation() {
    return ValueListenableBuilder<double>(
      valueListenable: _logoScaleController,
      builder: (context, scale, _) {
        return AnimatedScale(
          scale: scale,
          duration: const Duration(milliseconds: 300),
          child: IconHelper.loadAsset(
            Assets.appLogo,
            width: _logoWidth.gw,
            fit: BoxFit.contain,
          ),
        );
      },
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LogD('App lifecycle state changed: $state');
    if (state == AppLifecycleState.resumed) {
      _controller?.play();
      _configureSystemUI(); // Ensure UI settings are maintained
    } else if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _controller?.pause();
    }
  }

  @override
  void dispose() {
    LogD('Disposing splash screen');
    _cleanupVideoController();
    _logoScaleController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
