import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/main/widgets/draggable_float_widget.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/shared/widgets/custom_pin_keyboard.dart';
import 'package:pinput/pinput.dart';

class ThirdPartyPasswordDialog extends StatefulWidget {
  final String withdrawAmount;
  final int userBankId;
  final int? type;
  final int? channelId;
  const ThirdPartyPasswordDialog({
    super.key,
    required this.withdrawAmount,
    required this.userBankId,
    this.type,
    this.channelId,
  });

  @override
  State<ThirdPartyPasswordDialog> createState() => _ThirdPartyPasswordDialogState();
}

class _ThirdPartyPasswordDialogState extends State<ThirdPartyPasswordDialog> {
  final TextEditingController passwordController = TextEditingController();
  late PinTheme defaultPinTheme;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showKeyboard();
    });
  }

  @override
  void dispose() {
    passwordController.dispose();
    super.dispose();
  }

  void _showKeyboard() {
    FloatingWidgetManager().updatePosition(FloatingPosition.centerRight);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.transparent, // Lighter overlay
      builder: (_) => WithdrawalPasswordKeyboard(
        controller: passwordController,
        bottomPadding: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom,
        onSubmit: () {
          _submitWithdrawal();
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) Navigator.pop(context);
          });
        },
      ),
    ).whenComplete(() {
      FloatingWidgetManager().updatePosition(FloatingPosition.bottomRight);
    });
  }

  void _submitWithdrawal() {
    if (passwordController.text.length == 6) {
      context.read<ThirdPartyChannelCubit>().withdraw(
            withdrawAmount: widget.withdrawAmount,
            userBankId: widget.userBankId,
            password: passwordController.text,
            type: widget.type,
            channelId: widget.channelId,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    defaultPinTheme = PinTheme(
      width: 48,
      height: 48,
      textStyle: context.textTheme.primary.w500,
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(10.gr),
        border: Border.all(color: context.theme.primaryColor, width: 0.1),
      ),
    );

    return AlertDialog(
      backgroundColor: context.theme.cardColor,
      title: Text('pleaseEnterWithdrawPassword'.tr()),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: _showKeyboard,
            child: AbsorbPointer(
              child: Pinput(
                controller: passwordController,
                length: 6,
                readOnly: true,
                obscureText: true,
                defaultPinTheme: defaultPinTheme,
                focusedPinTheme: defaultPinTheme.copyWith(
                  decoration: defaultPinTheme.decoration!.copyWith(
                    border: Border.all(color: context.theme.primaryColor, width: 0.8),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
