import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart' show SvgPicture;
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/user_wallet/user_wallet_model.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_state.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel_entity_list_entity.dart';
import 'package:gp_stock_app/features/profile/logic/third_party_channel/third_party_channel_cubit.dart';
import 'package:gp_stock_app/features/profile/screens/third_party_channel/third_party_password_dialog.dart';
import 'package:gp_stock_app/features/profile/widgets/account_selection_bottom_sheet.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:text_scroll/text_scroll.dart';

enum ThirdPartyTransactionType {
  deposit,
  withdraw;

  int get value => switch (this) {
        ThirdPartyTransactionType.deposit => 1,
        ThirdPartyTransactionType.withdraw => 2,
      };
}

class ThirdPartyChannelScreen extends StatefulWidget {
  const ThirdPartyChannelScreen(
      {super.key, this.showAppBar = true, this.transactionType = ThirdPartyTransactionType.deposit});

  final bool showAppBar;
  final ThirdPartyTransactionType transactionType;

  @override
  State<ThirdPartyChannelScreen> createState() => _ThirdPartyChannelScreenState();
}

class _ThirdPartyChannelScreenState extends State<ThirdPartyChannelScreen> {
  int selectedPaymentMethod = 0;
  int selectedPaymentType = 0;
  final amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.read<ThirdPartyChannelCubit>().getThirdPartyChannelList(widget.transactionType.value);
    context.read<ThirdPartyChannelCubit>().getUserWalletList();
    if (widget.transactionType == ThirdPartyTransactionType.withdraw) {
      context.read<ThirdPartyChannelCubit>().getUserWalletList();
    }

    amountController.addListener(() {
      setState(() {}); // Trigger rebuild when amount changes
    });
  }

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  bool _canSubmit(BuildContext context, {required ThirdPartyChannelState state}) {
    if (amountController.text.trim().isEmpty) return false;

    if (state.channels.isEmpty) return false;
    if (selectedPaymentMethod >= state.channels.length) return false;

    final selectedChannel = state.channels[selectedPaymentMethod];
    if (selectedChannel.payTypeList.isEmpty) return false;
    if (selectedPaymentType >= selectedChannel.payTypeList.length) return false;

    final selectedPayType = selectedChannel.payTypeList[selectedPaymentType];
    if (selectedPayType.payTypeId == 0) return false;

    if (widget.transactionType == ThirdPartyTransactionType.withdraw) {
      if (state.selectedWallet == null) return false;
    }

    return context
        .read<ThirdPartyChannelCubit>()
        .validateAmount(selectedPaymentMethod, selectedPaymentType, amountController.text, showToast: false);
  }

  /// Handles withdraw submission
  void _handleWithdrawSubmit(ThirdPartyChannelPayType selectedPayType, ThirdPartyChannelState state) {
    showDialog(
      context: context,
      builder: (_) => BlocProvider.value(
        value: context.read<ThirdPartyChannelCubit>(),
        child: ThirdPartyPasswordDialog(
          withdrawAmount: amountController.text,
          userBankId: state.selectedWallet!.id!,
          channelId: selectedPayType.payTypeId,
          type: 3,
        ),
      ),
    );
  }

  /// Reset when payment method changes
  void _onPaymentMethodChanged(int newIndex) {
    if (selectedPaymentMethod != newIndex) {
      setState(() {
        selectedPaymentMethod = newIndex;
        selectedPaymentType = 0;
        amountController.clear();
      });

      // Reset wallet selection for withdraw
      if (widget.transactionType == ThirdPartyTransactionType.withdraw) {
        context.read<ThirdPartyChannelCubit>().updateSelectedWallet(null);
      }
    }
  }

  /// Simple payment type change
  void _onPaymentTypeChanged(int newIndex) {
    setState(() {
      selectedPaymentType = newIndex;
    });
  }

  /// Gets filtered wallets based on selected payment method
  List<UserWalletModel> _getFilteredWallets(ThirdPartyChannelState state) {
    if (state.wallets == null || state.wallets!.isEmpty) return [];
    if (state.channels.isEmpty || selectedPaymentMethod >= state.channels.length) {
      return state.wallets!;
    }

    final selectedChannel = state.channels[selectedPaymentMethod];
    return state.wallets!.where((wallet) {
      return selectedChannel.isNeedBind ? wallet.bankCode == selectedChannel.payWayCode : wallet.type != "WALLET";
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ThirdPartyChannelCubit, ThirdPartyChannelState>(
      listenWhen: (previous, current) => previous.withdrawStatus != current.withdrawStatus,
      listener: (context, state) {
        if (state.withdrawStatus.isSuccess) {
          amountController.clear();
        }
      },
      child: Scaffold(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        appBar: widget.showAppBar
            ? AppBar(
                title: Text(
                  'third'.tr(),
                ),
              )
            : null,
        body: BlocConsumer<ThirdPartyChannelCubit, ThirdPartyChannelState>(
          listener: (context, state) {
            if (state.updatingField == ThirdPartyChannelField.payment) {
              if (state.paymentStatus == DataStatus.success && state.paymentResponse != null) {
                amountController.clear();
              }
            }
          },
          builder: (context, state) {
            if (state.channelListStatus == DataStatus.loading) {
              return _buildLoadingShimmer();
            } else if (state.channelListStatus == DataStatus.success) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    _buildPaymentMethods(state.channels),
                    _buildPaymentMethods2(state.channels),
                    _buildWalletSelection(context),
                    _buildDepositAmount(channels: state.channels, controller: amountController, state: state),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: CustomMaterialButton(
                        isEnabled: _canSubmit(context, state: state),
                        onPressed: () {
                          final selectedChannel = state.channels[selectedPaymentMethod];
                          final selectedPayType = selectedChannel.payTypeList[selectedPaymentType];

                          switch (widget.transactionType) {
                            case ThirdPartyTransactionType.deposit:
                              context.read<ThirdPartyChannelCubit>().deposit(
                                    currentChannelIndex: selectedPaymentMethod,
                                    selectedPaymentTypeIndex: selectedPaymentType,
                                    amount: amountController.text,
                                  );
                            case ThirdPartyTransactionType.withdraw:
                              _handleWithdrawSubmit(selectedPayType, state);
                          }
                        },
                        buttonText: 'submit'.tr(),
                        color: context.theme.primaryColor,
                        borderColor: context.theme.primaryColor,
                        borderRadius: 5.gr,
                        textColor: Colors.white,
                        fontSize: 13.gr,
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    SupportWidget(),
                    const SizedBox(height: kBottomNavigationBarHeight),
                  ],
                ),
              );
            } else if (state.channelListStatus == DataStatus.failed) {
              return _buildErrorView(state.error ?? 'somethingWentWrong'.tr());
            } else {
              return const SizedBox.shrink();
            }
          },
        ),
      ),
    );
  }

  Widget _buildLoadingShimmer() {
    return Padding(
      padding: EdgeInsets.all(16.gr),
      child: Column(
        children: [
          // Payment Methods Section
          ShadowBox(
            child: ShimmerWidget(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    height: 24.gw,
                    width: 120.gw,
                    margin: EdgeInsets.all(16.gr),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Wrap(
                    spacing: 8.gr,
                    runSpacing: 8.gw,
                    children: List.generate(
                      3,
                      (index) => Container(
                        padding: EdgeInsets.all(16.gr),
                        width: 0.26.gsw,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.gr),
                          border: Border.all(
                            color: Colors.grey[200]!,
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Container(
                              width: 40.gw,
                              height: 40.gw,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                            SizedBox(height: 4.gw),
                            Container(
                              height: 16.gw,
                              width: 60.gw,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4.gr),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16.gw),
          // Payment Types Section
          ShadowBox(
            child: ShimmerWidget(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    height: 24.gw,
                    width: 100.gw,
                    margin: EdgeInsets.all(16.gr),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Wrap(
                    spacing: 8.gr,
                    runSpacing: 8.gw,
                    children: List.generate(
                      4,
                      (index) => Container(
                        height: 32.gw,
                        width: 80.gw,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.gr),
                          border: Border.all(
                            color: Colors.grey[200]!,
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16.gw),
          // Deposit Amount Section
          ShadowBox(
            child: ShimmerWidget(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    height: 24.gw,
                    width: 120.gw,
                    margin: EdgeInsets.all(16.gr),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Wrap(
                    spacing: 4.gr,
                    runSpacing: 8.gw,
                    children: List.generate(
                      6,
                      (index) => Container(
                        height: 32.gw,
                        width: 60.gw,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.gr),
                          border: Border.all(
                            color: Colors.grey[200]!,
                            width: 1,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 16.gw),
                  Container(
                    height: 48.gw,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods(List<ThirdPartyChannelEntity> channels) {
    return Container(
      padding: EdgeInsets.all(16.gr),
      child: ShadowBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'paymentMethods'.tr(),
              style: context.textTheme.title.w600,
            ),
            8.verticalSpace,
            if (channels.isNotEmpty)
              Wrap(
                spacing: 8.gr,
                runSpacing: 8.gw,
                children: channels.map((channel) {
                  final isSelected = selectedPaymentMethod == channels.indexOf(channel);
                  return Stack(
                    children: [
                      GestureDetector(
                        onTap: () => _onPaymentMethodChanged(channels.indexOf(channel)),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 16.gw, horizontal: 2.gw),
                          constraints: BoxConstraints(
                            maxWidth: 0.26.gsw,
                            minWidth: 0.26.gsw,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.gr),
                            border: Border.all(
                              color: isSelected ? context.theme.primaryColor : context.theme.dividerColor,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            spacing: 4.gw,
                            children: [
                              CachedNetworkImage(
                                imageUrl: channel.icon,
                                width: 40.gw,
                                height: 40.gw,
                                errorWidget: (context, url, error) => const SizedBox.shrink(),
                              ),
                              TextScroll(
                                channel.payWayName,
                                mode: TextScrollMode.endless,
                                velocity: const Velocity(pixelsPerSecond: Offset(20, 0)),
                                delayBefore: const Duration(milliseconds: 500),
                                style: context.textTheme.title,
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (channel.recommended)
                        Positioned(
                          top: 5,
                          right: 5,
                          child: SvgPicture.asset(
                            Assets.hot,
                            width: 22.gw,
                            height: 22.gw,
                            colorFilter: ColorFilter.mode(
                              isSelected ? context.theme.primaryColor : context.colorTheme.textRegular,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                    ],
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods2(List<ThirdPartyChannelEntity> channels) {
    if (channels.isEmpty) return const SizedBox.shrink();
    return Container(
      padding: EdgeInsets.all(16.gr),
      child: ShadowBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'paymentTypes'.tr(),
              style: context.textTheme.title.w600,
            ),
            if (channels.isNotEmpty) 8.verticalSpace,
            Wrap(
              spacing: 8.gr,
              runSpacing: 8.gw,
              children: channels[selectedPaymentMethod]
                  .payTypeList
                  .map(
                    (payType) => FilterChip(
                      label: Text(payType.controllerTips),
                      onSelected: (value) => _onPaymentTypeChanged(
                        channels[selectedPaymentMethod].payTypeList.indexOf(payType),
                      ),
                      showCheckmark: false,
                      backgroundColor: context.theme.cardColor,
                      selectedColor: context.theme.primaryColor,
                      labelStyle: context.textTheme.primary.fs13.copyWith(
                        color: selectedPaymentType == channels[selectedPaymentMethod].payTypeList.indexOf(payType)
                            ? context.colorTheme.buttonPrimary
                            : context.colorTheme.textRegular,
                      ),
                      selected: selectedPaymentType == channels[selectedPaymentMethod].payTypeList.indexOf(payType),
                      side: selectedPaymentType == channels[selectedPaymentMethod].payTypeList.indexOf(payType)
                          ? BorderSide.none
                          : BorderSide(
                              color: context.theme.dividerColor,
                              width: 1,
                            ),
                    ),
                  )
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletSelection(BuildContext ctx) {
    if (widget.transactionType == ThirdPartyTransactionType.deposit) return const SizedBox.shrink();
    return BlocListener<WithdrawalCubit, WithdrawalState>(
      listenWhen: (previous, current) => previous.withdrawStatus != current.withdrawStatus,
      listener: (context, state) {
        // if (state.withdrawStatus.isSuccess) {
        //   // Helper.showFlutterToast('withdrawnSuccessfully'.tr());
        //   amountController.clear();
        //   print('withdrawnSuccessfully');
        //   Navigator.pop(context);
        // } else if (state.withdrawStatus.isFailed) {
        //   Helper.showFlutterToast(state.error ?? 'invalidWithdrawPassword'.tr());
        // }
      },
      child: Padding(
        padding: EdgeInsets.all(16.gr),
        child: Column(
          children: [
            BlocBuilder<ThirdPartyChannelCubit, ThirdPartyChannelState>(
              builder: (context, state) {
                if (state.walletsFetchStatus == DataStatus.loading) {
                  return ShadowBox(
                    child: ShimmerWidget(
                      height: 50.gw,
                      width: double.infinity,
                    ),
                  );
                } else if (state.walletsFetchStatus == DataStatus.failed) {
                  return ShadowBox(
                    child: Padding(
                      padding: EdgeInsets.all(16.gr),
                      child: Text(
                        state.error ?? 'Failed to load wallets',
                        style: context.textTheme.regular.copyWith(color: Colors.red),
                      ),
                    ),
                  );
                } else if (state.walletsFetchStatus == DataStatus.success &&
                    state.channels.isNotEmpty &&
                    state.channels.length > selectedPaymentMethod) {
                  return GestureDetector(
                    onTap: () {
                      _showAccountSelectionBottomSheet(context, parentContext: ctx);
                    },
                    child: ShadowBox(
                      padding: EdgeInsets.all(16.gw),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 12.gw,
                        children: [
                          Text(
                            'withdraw_select_fund_type'.tr(),
                            style: context.textTheme.title.w600,
                          ),
                          TextFieldWidget(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_sharp,
                              size: 24.gw,
                            ),
                            readOnly: true,
                            onTap: () => _showAccountSelectionBottomSheet(context, parentContext: ctx),
                            controller: TextEditingController(
                                text:
                                    '${state.selectedWallet?.bankCode ?? ''} ${state.selectedWallet?.payAddress ?? ''}'),
                          )
                        ],
                      ),
                    ),
                  );
                } else {
                  return ShadowBox(
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16.gr),
                      alignment: Alignment.center,
                      child: Text(
                        'no_available_payment_account'.tr(),
                        style: context.textTheme.regular,
                      ),
                    ),
                  );
                }
              },
            ),
            SizedBox(height: 8.gw),
            // Add Wallet Button
            if (widget.transactionType == ThirdPartyTransactionType.deposit)
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      getIt<NavigatorService>().push(AppRouter.routeAddWallet).then((result) {
                        if (result == true && mounted) {
                          // Refresh wallet list after successful addition
                          context.read<ThirdPartyChannelCubit>().getUserWalletList();
                        }
                      });
                    },
                    icon: const Icon(Icons.add, size: 18, color: Colors.white),
                    label: Text(
                      'addAccount'.tr(),
                      style: context.textTheme.primary.fs12.copyWith(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.theme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  void _showAccountSelectionBottomSheet(
    BuildContext context, {
    required BuildContext parentContext,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: false,
      backgroundColor: Colors.transparent,
      builder: (_) => BlocProvider.value(
        value: context.read<ThirdPartyChannelCubit>(),
        child: BlocBuilder<ThirdPartyChannelCubit, ThirdPartyChannelState>(
          builder: (context, state) {
            return AccountSelectionBottomSheet(
              onAddAccount: () {
                Navigator.of(context).pop();
                getIt<NavigatorService>().push(AppRouter.routeAddWallet).then((result) {
                  if (result == true && parentContext.mounted) {
                    parentContext.read<ThirdPartyChannelCubit>().getUserWalletList();
                  }
                });
              },
              editMode: state.isEditMode,
              list: _buildAccountList(context),
              onManageClick: () {
                context.read<ThirdPartyChannelCubit>().updateEditMode();
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildAccountList(
    BuildContext context,
  ) {
    return BlocSelector<
        ThirdPartyChannelCubit,
        ThirdPartyChannelState,
        ({
          List<UserWalletModel> accounts,
          UserWalletModel? selectedAccount,
          bool isEditMode,
          ({int? id, DataStatus? status}) unbindWalletStatus
        })>(
      selector: (state) => (
        accounts: _getFilteredWallets(state),
        selectedAccount: state.selectedWallet,
        isEditMode: state.isEditMode,
        unbindWalletStatus: (id: state.unbindWalletStatus?.id, status: state.unbindWalletStatus?.status)
      ),
      builder: (context, state) => Expanded(
          child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 10.gw),
        itemCount: state.accounts.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: context.colorTheme.border,
          thickness: 0.5,
        ),
        itemBuilder: (context, index) {
          final account = state.accounts[index];
          final isSelected = state.selectedAccount?.id == account.id;

          return _buildAccountItem(
            context,
            account: account,
            isSelected: isSelected,
            accountName: getIt<UserCubit>().currentUser?.realName ?? '',
            onAccountSelected: (account) {
              context.read<ThirdPartyChannelCubit>().updateSelectedWallet(account);
              Navigator.of(context).pop();
            },
            isEditMode: state.isEditMode,
            unbindWalletStatus: state.unbindWalletStatus,
          );
        },
      )),
    );
  }

  Widget _buildAccountItem(
    BuildContext context, {
    required UserWalletModel account,
    required bool isSelected,
    required String accountName,
    required Function(UserWalletModel?) onAccountSelected,
    required bool isEditMode,
    required ({int? id, DataStatus? status}) unbindWalletStatus,
  }) {
    final width = (1.gsw / 3) - (10.gw * 2);
    final style = context.textTheme.title;
    return InkWell(
      onTap: isEditMode ? null : () => onAccountSelected(account),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.gw),
        child: Row(
          children: [
            SizedBox(
              width: width,
              child: Text(accountName, style: style, textAlign: TextAlign.left),
            ),
            Expanded(
              child: Text(account.payAddress ?? '', style: style, textAlign: TextAlign.center),
            ),
            if (isEditMode)
              InkWell(
                onTap: () {
                  context.read<ThirdPartyChannelCubit>().unbindUserWallet(account.id!);
                },
                child: unbindWalletStatus.id == account.id && unbindWalletStatus.status == DataStatus.loading
                    ? Container(
                        width: width,
                        height: 18.gw,
                        alignment: Alignment.centerRight,
                        child: SizedBox(
                          width: 18.gw,
                          height: 18.gw,
                          child: CircularProgressIndicator(
                            color: context.colorTheme.stockRed,
                            strokeWidth: 1,
                          ),
                        ),
                      )
                    : Container(
                        width: width,
                        height: 18.gw,
                        alignment: Alignment.centerRight,
                        child: Icon(Icons.delete_outlined, color: context.colorTheme.stockRed),
                      ),
              )
            else
              Container(
                width: width,
                alignment: Alignment.centerRight,
                child: Container(
                  width: 18.gw,
                  height: 18.gw,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? context.theme.primaryColor : Colors.transparent,
                    border: Border.all(
                      color: isSelected ? context.theme.primaryColor : context.colorTheme.border,
                      width: 1,
                    ),
                  ),
                  child: isSelected ? Icon(Icons.check, size: 10.gw, color: context.textTheme.secondary.color) : null,
                ),
              ),
            SizedBox(width: 15.gw),
          ],
        ),
      ),
    );
  }

  Widget _buildDepositAmount(
      {required List<ThirdPartyChannelEntity> channels,
      required TextEditingController controller,
      required ThirdPartyChannelState state}) {
    String min = '0';
    String max = '0';
    if (channels.isNotEmpty) {
      min = channels[selectedPaymentMethod].payTypeList[selectedPaymentType].amountMinLimit.toStringAsFixed(0);
      max = channels[selectedPaymentMethod].payTypeList[selectedPaymentType].amountMaxLimit.toStringAsFixed(0);
    }

    return Container(
      padding: EdgeInsets.all(16.gr),
      child: ShadowBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.transactionType == ThirdPartyTransactionType.deposit
                  ? 'depositAmount'.tr()
                  : "withdrawalAmount".tr(),
              style: context.textTheme.title.w600,
            ),
            if (channels.isNotEmpty) ...[
              8.verticalSpace,
              Wrap(
                spacing: 4.gr,
                children: channels[selectedPaymentMethod]
                    .payTypeList
                    .expand(
                      (payType) => payType.amountList.split(',').map(
                            (amount) => FilterChip(
                              label: Text(amount),
                              onSelected: (value) {
                                if (context.read<ThirdPartyChannelCubit>().validateAmount(
                                      selectedPaymentMethod,
                                      selectedPaymentType,
                                      amount,
                                    )) {
                                  setState(() {
                                    controller.text = amount;
                                  });
                                }
                              },
                              showCheckmark: false,
                              backgroundColor: context.theme.cardColor,
                              selectedColor: context.theme.scaffoldBackgroundColor,
                              labelStyle: context.textTheme.primary.fs13.copyWith(
                                color: context.colorTheme.textRegular,
                              ),
                              side: BorderSide(
                                color: context.theme.dividerColor,
                                width: 1,
                              ),
                            ),
                          ),
                    )
                    .toList(),
              ),
            ],
            8.verticalSpace,
            TextFieldWidget(
              controller: controller,
              borderType: TextFieldBorderType.outline,
              contentPadding: EdgeInsets.symmetric(horizontal: 16),
              hintText: '$min ~ $max',
              textInputType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChanged: (value) => setState(() {}),
            ),
            16.verticalSpace,
            BlocSelector<AccountInfoCubit, AccountInfoState, ({AccountInfoData? accountInfoData})>(
              selector: (state) => (accountInfoData: state.accountInfo),
              builder: (context, accountState) {
                return Column(
                  children: [
                    Row(
                      spacing: 8,
                      children: [
                        Text(
                          'availableBalance'.tr(),
                          style: context.textTheme.regular.fs13,
                        ),
                        FlipText(
                          accountState.accountInfoData?.usableCash ?? 0,
                        )
                      ],
                    ),
                  ],
                );
              },
            ),
            4.verticalSpace,
            Text(
              '* ${'minimumAmount'.tr()}: $min , ${'maximumAmount'.tr()}: $max',
              style: context.textTheme.regular.fs12.copyWith(
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48.gr,
            color: context.colorTheme.stockRed,
          ),
          16.verticalSpace,
          Text(
            errorMessage,
            style: context.textTheme.primary.fs16.copyWith(
              color: context.colorTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          24.verticalSpace,
          ElevatedButton(
            onPressed: () {
              context.read<ThirdPartyChannelCubit>().getThirdPartyChannelList(widget.transactionType.value);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: context.theme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.gr),
              ),
              padding: EdgeInsets.symmetric(horizontal: 24.gw, vertical: 12.gw),
            ),
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }
}
