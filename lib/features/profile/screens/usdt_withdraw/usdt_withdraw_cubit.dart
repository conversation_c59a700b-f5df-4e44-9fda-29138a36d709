import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/wallet.dart';
import 'package:gp_stock_app/core/models/apis/withdraw.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:injectable/injectable.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/common_utils.dart';

import 'usdt_withdraw_state.dart';

@injectable
class UsdtWithdrawCubit extends Cubit<UsdtWithdrawState> {
  UsdtWithdrawCubit() : super(const UsdtWithdrawState()) {
    fetchUsdtChannels().then((_) => fetchUsdtWalletAddressList());
  }

  Future<void> fetchUsdtChannels() async {
    emit(state.copyWith(isLoadingChannels: true));
    try {
      final result = await WithdrawApi.fetchUsdtWithdrawList();
      emit(state.copyWith(
        usdtChannels: result,
        selectedChannel: result.isNotEmpty ? result.first : null,
        isLoadingChannels: false,
      ));
    } catch (e) {
      emit(state.copyWith(isLoadingChannels: false));
    }
  }

  void selectChannel(int channelId) {
    final selectedChannel = state.usdtChannels
        .where(
          (channel) => channel.id == channelId,
        )
        .firstOrNull;
    USDTWallet? defaultWallet;
    try {
      defaultWallet = state.usdtWalletAddresses
          .firstWhereOrNull((wallet) => wallet.isWithdrawDefault == 1 && wallet.network == selectedChannel!.network);
      defaultWallet ??=
          state.usdtWalletAddresses.firstWhereOrNull((wallet) => wallet.network == selectedChannel!.network);
    } catch (e) {
      defaultWallet = null;
    }
    if (selectedChannel != null) {
      emit(state.copyWith(
        selectedChannel: selectedChannel,
        withdrawalAmount: () => null, // Clear recharge amount when channel changes
        selectedWalletAddress: () => defaultWallet,
        isAmountValid: true, // Reset validation when channel changes
      ));
    }
  }

  void updateAmount(String amount) {
    emit(state.copyWith(selectedAmount: () => amount));
  }

  void updateRechargeAmount(String amount) {
    emit(state.copyWith(withdrawalAmount: () => amount));
  }

  void updateAmountValidation(bool isValid) {
    emit(state.copyWith(isAmountValid: isValid));
  }

  Future<void> fetchUsdtWalletAddressList() async {
    emit(state.copyWith(isLoadingWalletAddresses: true));
    try {
      final result = await WalletApi.fetchUsdtWalletAddressList();

      USDTWallet? defaultWallet;
      try {
        defaultWallet = result.firstWhereOrNull(
            (wallet) => wallet.isWithdrawDefault == 1 && wallet.network == state.selectedChannel!.network);
        defaultWallet ??= result.firstWhereOrNull((wallet) => wallet.network == state.selectedChannel!.network);
      } catch (e) {
        defaultWallet = null;
      }

      emit(state.copyWith(
        usdtWalletAddresses: result,
        selectedWalletAddress: () => defaultWallet,
        isLoadingWalletAddresses: false,
      ));
    } catch (e) {
      emit(state.copyWith(isLoadingWalletAddresses: false));
    }
  }

  void selectWalletAddress(USDTWallet wallet) {
    emit(state.copyWith(selectedWalletAddress: () => wallet));
  }

  List<USDTWallet> getFilteredWalletsByNetwork(int network) {
    return state.usdtWalletAddresses.where((wallet) => wallet.network == network).toList();
  }

  Future<void> submitWithdraw({
    required String password,
  }) async {
    if (state.selectedChannel == null ||
        state.selectedWalletAddress == null ||
        state.withdrawalAmount == null ||
        state.withdrawalAmount!.isEmpty) {
      return;
    }

    emit(state.copyWith(submitStatus: DataStatus.loading));
    try {
      final amount = double.parse(state.withdrawalAmount!);
      final result = await WithdrawApi.applyUSDTWithdraw(
        amount: amount,
        id: state.selectedChannel!.id,
        password: password.toBase64,
        walletId: state.selectedWalletAddress!.id,
      );

      emit(state.copyWith(
        submitStatus: result ? DataStatus.success : DataStatus.failed,
      ));
      if (result) {
        Helper.showFlutterToast('withdrawalSubmittedSuccessfully'.tr());
      }
      getIt<NavigatorService>().popUntil(AppRouter.routeWithdrawMain);
    } catch (e) {
      emit(state.copyWith(submitStatus: DataStatus.failed));
    }
  }

  void clearForm() {
    emit(state.copyWith(
      selectedChannel: null,
      selectedAmount: () => null,
      withdrawalAmount: () => null,
      selectedWalletAddress: null,
      submitStatus: DataStatus.idle,
      isAmountValid: true,
    ));
  }
}
