import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/withdraw_channel_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/string_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_input_formatter/no_leading_zero_int_formatter.dart';

import 'usdt_withdraw_cubit.dart';
import 'usdt_withdraw_state.dart';
import 'widgets/usdt_withdraw_password_dialog.dart';
import 'widgets/wallet_address_selection_bottom_sheet.dart';

class UsdtWithdrawView extends StatefulWidget {
  const UsdtWithdrawView({super.key});

  @override
  State<UsdtWithdrawView> createState() => _UsdtWithdrawViewState();
}

class _UsdtWithdrawViewState extends State<UsdtWithdrawView> {
  final TextEditingController _amountController = TextEditingController();

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _amountController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_onAmountChanged);
    context.read<UsdtWithdrawCubit>().stream.listen((state) {
      if (state.withdrawalAmount != null && _amountController.text != state.withdrawalAmount) {
        _amountController.text = state.withdrawalAmount!;
      } else if (state.withdrawalAmount == null && _amountController.text.isNotEmpty) {
        _amountController.clear();
      }
    });
  }

  void _onAmountChanged() {
    final cubit = context.read<UsdtWithdrawCubit>();
    final isValid = _validateAmount(_amountController.text);
    cubit.updateAmountValidation(isValid);
  }

  bool _validateAmount(String value) {
    if (value.isEmpty) return true;
    try {
      final amount = double.parse(value);
      final selected = context.read<UsdtWithdrawCubit>().state.selectedChannel;
      final min = selected?.minWithdrawalAmount ?? 0;
      final max = selected?.maxWithdrawalAmount ?? double.infinity;
      return amount >= min && amount <= max;
    } catch (_) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<UsdtWithdrawCubit>(context);
    return BlocListener<UsdtWithdrawCubit, UsdtWithdrawState>(
      listener: (context, state) {
        switch (state.submitStatus) {
          case DataStatus.success:
            cubit.clearForm();
            _amountController.clear();
            break;
          default:
            break;
        }
      },
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16,
              children: [
                _buildChannelDropdown(context, cubit),
                _buildWalletAddressSelection(context, cubit),
                _buildRechargeAmountSection(context, cubit),
                SizedBox(height: 100..gw), // Space for bottom navigation bar
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          padding: EdgeInsets.all(16.gw),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSubmitButton(context, cubit),
              SizedBox(height: 16..gw),
              SupportWidget(),
              SizedBox(height: 16..gw),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChannelDropdown(BuildContext context, UsdtWithdrawCubit cubit) {
    return ShadowBox(
      child: BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
        builder: (context, state) {
          if (state.isLoadingChannels) {
            return _buildChannelDropdownShimmer();
          }

          final dropdownItems = _convertChannelsToDropdownValues(state.usdtChannels);
          final selectedItem = state.selectedChannel != null
              ? _convertChannelToDropdownValue(state.selectedChannel!)
              : (state.usdtChannels.isNotEmpty ? _convertChannelToDropdownValue(state.usdtChannels.first) : null);

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 4.gw,
                children: [
                  Text(
                    'withdrawChannel'.tr(),
                    style: context.textTheme.regular.copyWith(
                      fontSize: 14.gsp,
                      fontWeight: FontWeight.w500,
                      color: context.colorTheme.textTitle,
                    ),
                  ),
                  if (state.selectedChannel?.minWithdrawalAmount != null &&
                      state.selectedChannel?.maxWithdrawalAmount != null)
                    Text(
                      'single_transaction_limit'.tr(args: [
                        state.selectedChannel!.minWithdrawalAmount.formattedMoney,
                        state.selectedChannel!.maxWithdrawalAmount.formattedMoney
                      ]), // 单笔限额
                      style: context.textTheme.regular.copyWith(
                        color: context.theme.hintColor,
                      ),
                    ),
                ],
              ),
              SizedBox(height: 8..gw),
              CommonDropdown<DropDownValue>(
                hintText: 'pleaseSelectRechargeNetwork'.tr(),
                dropDownValue: dropdownItems,
                selectedItem: selectedItem,
                onChanged: (DropDownValue? value) {
                  if (value?.code != null) {
                    cubit.selectChannel(value!.code as int);
                  }
                },
                showSearchBox: false,
                borderRadius: 8.gr,
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildChannelDropdownShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ShimmerWidget(
          height: 16..gw,
          width: 120.gw,
          radius: 4.gr,
        ),
        SizedBox(height: 8..gw),
        ShimmerWidget(
          height: 48..gw,
          width: double.infinity,
          radius: 8.gr,
        ),
      ],
    );
  }

  Widget _buildWalletAddressSelection(BuildContext context, UsdtWithdrawCubit cubit) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        return ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'withdrawalAddress'.tr(),
                style: context.textTheme.regular.copyWith(
                  fontSize: 14.gsp,
                  fontWeight: FontWeight.w500,
                  color: context.colorTheme.textTitle,
                ),
              ),
              SizedBox(height: 8..gw),
              GestureDetector(
                onTap: () {
                  _showWalletAddressBottomSheet(context, cubit, state);
                },
                child: Stack(
                  alignment: Alignment.topRight,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 77..gw,
                      decoration: BoxDecoration(
                        color: context.theme.inputDecorationTheme.fillColor,
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                      child: Center(
                        child: Row(
                          mainAxisAlignment:
                              state.selectedWalletAddress == null ? MainAxisAlignment.center : MainAxisAlignment.start,
                          children: [
                            if (state.selectedWalletAddress == null) ...[
                              Icon(
                                Icons.add,
                                size: 16.gw,
                                color: context.theme.hintColor,
                              ),
                              SizedBox(width: 13.gw),
                            ],
                            Padding(
                              padding: const EdgeInsets.only(left: 16),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (state.selectedWalletAddress != null) ...[
                                    Text(
                                      _getNetworkName(state.selectedWalletAddress!.network),
                                      style: context.textTheme.title.w600,
                                    ),
                                    SizedBox(height: 8..gw),
                                  ],
                                  Text(
                                    state.selectedWalletAddress != null
                                        ? state.selectedWalletAddress!.walletAddress
                                        : 'addWithdrawalAddress'.tr(),
                                    style: context.textTheme.regular.copyWith(
                                      fontSize: 12.gsp,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (state.selectedWalletAddress != null)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 4..gw),
                        decoration: BoxDecoration(
                          color: context.theme.primaryColor,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(8.gr),
                            bottomLeft: Radius.circular(8.gr),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          spacing: 6,
                          children: [
                            Text(
                              "replace".tr(),
                              style: context.textTheme.secondary.copyWith(
                                fontSize: 12.gsp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 12.gsp,
                              color: context.textTheme.secondary.color,
                            ),
                          ],
                        ),
                      )
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showWalletAddressBottomSheet(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    if (state.selectedChannel == null) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => BlocProvider.value(
        value: cubit,
        child: WalletAddressSelectionBottomSheet(
          onWalletSelected: (wallet) {
            cubit.selectWalletAddress(wallet);
          },
        ),
      ),
    );
  }

  List<DropDownValue> _convertChannelsToDropdownValues(List<WithdrawChannel> channels) {
    return channels.map((channel) => _convertChannelToDropdownValue(channel)).toList();
  }

  DropDownValue _convertChannelToDropdownValue(WithdrawChannel channel) {
    final networkName = _getNetworkName(channel.network);
    return DropDownValue(
      id: channel.id.toString(),
      value: networkName,
      code: channel.id,
    );
  }

  String _getNetworkName(int network) {
    switch (network) {
      case 1:
        return 'TRC20';
      case 2:
        return 'ERC20';
      case 3:
        return 'BEP20';
      default:
        return 'Unknown';
    }
  }

  Widget _buildRechargeAmountSection(BuildContext context, UsdtWithdrawCubit cubit) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        return ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 12.gw,
            children: [
              _buildRechargeAmountHeader(context, state),
              _buildAmountOptionsGrid(context, cubit, state),
              _buildAmountTextField(context, cubit, state),
              _buildAvailableBalanceWidget(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRechargeAmountHeader(BuildContext context, UsdtWithdrawState state) {
    return Wrap(
      spacing: 4.gw,
      children: [
        Text(
          'withdrawalAmount'.tr(), // 提现金额
          style: context.textTheme.title.w500,
        ),
        if (state.selectedChannel != null)
          Text(
            'current_exchange_rate'.tr(args: [state.selectedChannel!.exchangeRate.toString()]),
            textAlign: TextAlign.right,
            style: context.textTheme.secondary.copyWith(
              color: context.theme.hintColor,
            ),
          ),
      ],
    );
  }

  Widget _buildAmountOptionsGrid(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    final options = state.selectedChannel?.withdrawalAmountOptionsList ?? [];

    if (options.isEmpty) {
      return const SizedBox.shrink();
    }

    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: options.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          childAspectRatio: 100 / 28, //宽高比
          crossAxisSpacing: 10.gw, //水平间距
          mainAxisSpacing: 12.gw, //垂直间距
          crossAxisCount: 3),
      padding: EdgeInsets.zero,
      itemBuilder: (BuildContext context, int index) {
        final amount = options[index];
        return _buildAmountOptionButton(context, cubit, state, amount);
      },
    );
  }

  Widget _buildAmountOptionButton(
      BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state, String amount) {
    final isSelected = state.withdrawalAmount == amount;

    return GestureDetector(
      onTap: () {
        cubit.updateRechargeAmount(amount);
        _amountController.text = amount;
      },
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? context.theme.primaryColor : context.theme.inputDecorationTheme.fillColor,
          borderRadius: BorderRadius.circular(6.gr),
        ),
        child: Center(
          child: Text(
            amount,
            style: context.textTheme.title.w500.copyWith(
              color: isSelected ? context.colorTheme.textSecondary : null,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAmountTextField(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    return TextFieldWidget(
      controller: _amountController,
      textInputType: TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        NoLeadingZeroIntFormatter(),
      ],
      errorText: !state.isAmountValid ? 'amount_exceeds_limit_range'.tr() : null, // 金额超出限额范围
      onChanged: (value) {
        if (value == null) return;
        cubit.updateRechargeAmount(value);
      },
      decoration: InputDecoration(
        hintText: 'enterRechargeAmount'.tr(),
        hintStyle: context.textTheme.regular.copyWith(
          fontSize: 14.gsp,
          color: const Color(0xFFAFB8CB),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.gr),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.gr),
          borderSide: BorderSide(color: context.theme.primaryColor),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 11.gw),
      ),
    );
  }

  Widget _buildAvailableBalanceWidget() {
    return BlocSelector<AccountInfoCubit, AccountInfoState, ({AccountInfoData? accountInfoData})>(
      selector: (state) => (accountInfoData: state.accountInfo),
      builder: (context, accountState) {
        return Column(
          children: [
            Row(
              spacing: 8,
              children: [
                Text(
                  'availableBalance'.tr(),
                  style: context.textTheme.regular.fs13,
                ),
                FlipText(
                  accountState.accountInfoData?.usableCash ?? 0,
                )
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildSubmitButton(BuildContext context, UsdtWithdrawCubit cubit) {
    return BlocBuilder<UsdtWithdrawCubit, UsdtWithdrawState>(
      builder: (context, state) {
        final isEnabled = _canSubmit(state);

        return CustomMaterialButton(
          isEnabled: isEnabled,
          onPressed: isEnabled ? () => _handleSubmit(context, cubit, state) : null,
          buttonText: 'submit'.tr(),
          color: context.theme.primaryColor,
          borderColor: context.theme.primaryColor,
          borderRadius: 5.gr,
          textColor: Colors.white,
          fontSize: 13.gr,
        );
      },
    );
  }

  bool _canSubmit(UsdtWithdrawState state) {
    return state.selectedChannel != null &&
        state.selectedWalletAddress != null &&
        state.withdrawalAmount != null &&
        state.withdrawalAmount!.isNotEmpty &&
        !state.isLoadingChannels &&
        !state.isLoadingWalletAddresses &&
        !state.submitStatus.isLoading &&
        state.isAmountValid;
  }

  void _handleSubmit(BuildContext context, UsdtWithdrawCubit cubit, UsdtWithdrawState state) {
    // Validate all required fields
    if (state.selectedChannel == null) {
      // Show error for missing channel
      return;
    }

    if (state.selectedWalletAddress == null) {
      // Show error for missing wallet address
      return;
    }

    if (state.withdrawalAmount == null || state.withdrawalAmount!.isEmpty) {
      // Show error for missing amount
      return;
    }

    // Show password dialog
    _showPasswordDialog(context, cubit);
  }

  void _showPasswordDialog(BuildContext context, UsdtWithdrawCubit cubit) {
    showDialog(
      context: context,
      builder: (context) => UsdtWithdrawPasswordDialog(
        onSubmit: (password) {
          cubit.submitWithdraw(password: password);
        },
      ),
    );
  }
}
