import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/alipay.dart';
import 'package:gp_stock_app/core/models/apis/third_party.dart';
import 'package:gp_stock_app/core/models/apis/withdraw.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/account/domain/models/user_wallet/user_wallet_model.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:injectable/injectable.dart';

import 'alipay_withdraw_state.dart';

@injectable
class AlipayWithdrawCubit extends Cubit<AlipayWithdrawState> {
  AlipayWithdrawCubit() : super(const AlipayWithdrawState()) {
    _initializeWithdrawConfig();
    _fetchUserWalletList();
  }

  /// Initialize withdraw configuration
  Future<void> _initializeWithdrawConfig() async {
    emit(state.copyWith(withdrawConfigStatus: DataStatus.loading));
    try {
      final config = await AlipayApi.fetchAlipayWithdrawConfig();
      emit(state.copyWith(withdrawConfigEntity: config, withdrawConfigStatus: DataStatus.success));
    } catch (e) {
      emit(state.copyWith(withdrawConfigStatus: DataStatus.failed));
    }
  }

  Future<void> _fetchUserWalletList() async {
    emit(state.copyWith(alipayAccountStatus: DataStatus.loading));
    try {
      final response = await getIt<BankRepository>().getUserWalletList();
      emit(state.copyWith(
          alipayAccounts: response.data?.where((e) => e.type == 'ALIPAY').toList(),
          alipayAccountStatus: DataStatus.success));
    } catch (e) {
      emit(state.copyWith(alipayAccountStatus: DataStatus.failed));
    }
  }

  void updateSelectedAlipayAccount(UserWalletModel? account) {
    emit(state.copyWith(selectedAlipayAccount: account));
  }

  /// Update withdrawal amount
  void updateWithdrawalAmount(String amount) {
    final isValid = _validateAmount(amount);
    emit(state.copyWith(
      withdrawalAmount: () => int.tryParse(amount) ?? 0,
      isAmountValid: isValid,
    ));
  }

  /// Validate withdrawal amount
  bool _validateAmount(String amount) {
    if (amount.trim().isEmpty) return true;

    final double? parsedAmount = double.tryParse(amount);
    if (parsedAmount == null) return false;

    if (state.withdrawConfigEntity?.minWithdrawalAmount != null &&
        parsedAmount < state.withdrawConfigEntity!.minWithdrawalAmount) {
      return false;
    }
    if (state.withdrawConfigEntity?.maxWithdrawalAmount != null &&
        parsedAmount > state.withdrawConfigEntity!.maxWithdrawalAmount) {
      return false;
    }

    return true;
  }

  /// Submit withdrawal request
  Future<void> submitWithdraw({required String password}) async {
    if (!state.canSubmit) return;

    emit(state.copyWith(submitStatus: DataStatus.loading));

    try {
      final flag = await WithdrawApi.applyWithdraw(
          password: password.toBase64(),
          userBankCardId: state.selectedAlipayAccount!.id!,
          amount: state.withdrawalAmount!,
          channelId: state.withdrawConfigEntity?.id,
          type: 5 // Alipay
          );

      if (flag) {
        Helper.showFlutterToast('withdrawalSubmittedSuccessfully'.tr());
      }
      getIt<NavigatorService>().popUntil(AppRouter.routeWithdrawMain);
    } catch (e) {
      Helper.showFlutterToast('withdrawalSubmissionFailed'.tr());
    } finally {
      emit(state.copyWith(submitStatus: DataStatus.idle));
    }
  }

  /// Reset form
  void resetForm() {
    emit(state.copyWith(
      withdrawalAmount: () => null,
      submitStatus: DataStatus.idle,
      isAmountValid: true,
    ));
  }

  Future<void> bindUserWallet({
    required String payAddress,
  }) async {
    emit(state.copyWith(bindWalletStatus: DataStatus.loading));

    try {
      final result = await ThirdPartyService.bindUserWallet(
        bankCode: 'ALIPAY',
        payAddress: payAddress,
      );

      if (result) {
        emit(state.copyWith(
          bindWalletStatus: DataStatus.success,
        ));
        GPEasyLoading.showToast('addSuccess'.tr());
        await _fetchUserWalletList();
      } else {
        emit(state.copyWith(
          bindWalletStatus: DataStatus.failed,
        ));
        GPEasyLoading.showToast('addFailed'.tr());
      }
    } catch (e) {
      emit(state.copyWith(
        bindWalletStatus: DataStatus.failed,
      ));
      GPEasyLoading.showToast('addFailedWithError'.tr(args: [e.toString()]));
    }
  }

  void updateEditMode() {
    emit(state.copyWith(isEditMode: !state.isEditMode));
  }

  Future<void> unbindUserWallet(int id) async {
    emit(state.copyWith(unbindWalletStatus: (id: id, status: DataStatus.loading)));
    try {
      final isSuccess = await AlipayApi.unbindUserWallet(id);

      if (isSuccess) {
        emit(state.copyWith(
          unbindWalletStatus: (id: null, status: DataStatus.success),
        ));
        GPEasyLoading.showToast('deleteSuccess'.tr());
        await _fetchUserWalletList();
      } else {
        emit(state.copyWith(
          unbindWalletStatus: (id: null, status: DataStatus.failed),
        ));
        GPEasyLoading.showToast('deleteFailed'.tr());
      }
    } catch (e) {
      emit(state.copyWith(
        unbindWalletStatus: (id: null, status: DataStatus.failed),
      ));
      GPEasyLoading.showToast('deleteFailedWithError'.tr(args: [e.toString()]));
    }
  }
}
