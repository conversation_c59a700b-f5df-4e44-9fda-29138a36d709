import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/user_wallet/user_wallet_model.dart';
import 'package:gp_stock_app/features/profile/widgets/add_alipay_account_dialog.dart';
import 'package:gp_stock_app/features/profile/widgets/account_selection_bottom_sheet.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/support_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:gp_stock_app/shared/widgets/text_input_formatter/no_leading_zero_int_formatter.dart';

import 'alipay_withdraw_cubit.dart';
import 'alipay_withdraw_state.dart';
import 'widgets/alipay_withdraw_password_dialog.dart';

class AlipayWithdrawView extends StatefulWidget {
  const AlipayWithdrawView({super.key});

  @override
  State<AlipayWithdrawView> createState() => _AlipayWithdrawViewState();
}

class _AlipayWithdrawViewState extends State<AlipayWithdrawView> {
  final TextEditingController _amountController = TextEditingController();

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _setupControllerListeners();
  }

  void _setupControllerListeners() {
    _amountController.addListener(() {
      context.read<AlipayWithdrawCubit>().updateWithdrawalAmount(_amountController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AlipayWithdrawCubit, AlipayWithdrawState>(
      listenWhen: (previous, current) => previous.submitStatus != current.submitStatus,
      listener: (context, state) {
        if (state.submitStatus == DataStatus.success) {
          context.read<AlipayWithdrawCubit>().resetForm();
          _clearControllers();
        }
      },
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 16,
              children: [
                _buildAlipayAccountSection(context),
                _buildAmountSection(context),
                SizedBox(height: 100.gw), // Space for bottom navigation bar
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          padding: EdgeInsets.all(16.gw),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSubmitButton(context),
              SizedBox(height: 8.gw),
              _buildChannelInfoText(context),
              SizedBox(height: 16.gw),
              SupportWidget(),
              SizedBox(height: 16.gw),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlipayAccountSection(BuildContext context) {
    return BlocSelector<AlipayWithdrawCubit, AlipayWithdrawState, UserWalletModel?>(
      selector: (state) => state.selectedAlipayAccount,
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            _showAccountSelectionBottomSheet(context);
          },
          child: ShadowBox(
            borderRadius: BorderRadius.circular(8.gr),
            padding: EdgeInsets.all(16.gw),
            child: Row(
              children: [
                Text(
                  'addAccount'.tr(),
                  style: context.textTheme.title,
                ),
                const Spacer(),
                Text(state?.payAddress ?? '', style: context.textTheme.title.w500),
                SizedBox(width: 8.gw),
                Icon(
                  Icons.keyboard_arrow_down_sharp,
                  size: 24.gw,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAmountSection(BuildContext context) {
    return BlocBuilder<AlipayWithdrawCubit, AlipayWithdrawState>(
      builder: (context, state) {
        return ShadowBox(
          borderRadius: BorderRadius.circular(8.gr),
          padding: EdgeInsets.all(16.gw),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 12.gw,
            children: [
              Text(
                  '${'enterWithdrawAmount'.tr()} ${state.selectedAlipayAccount != null ? '(${'lowest'.tr()} ${state.withdrawConfigEntity?.minWithdrawalAmount} 元)' : ''}',
                  style: context.textTheme.title),
              _buildAmountTextField(context, state),
              BlocSelector<AccountInfoCubit, AccountInfoState, ({AccountInfoData? accountInfoData})>(
                selector: (state) => (accountInfoData: state.accountInfo),
                builder: (context, accountState) {
                  return Column(
                    children: [
                      Row(
                        spacing: 8,
                        children: [
                          Text(
                            'balance'.tr(),
                            style: context.textTheme.regular.fs13,
                          ),
                          FlipText(
                            accountState.accountInfoData?.usableCash ?? 0,
                          )
                        ],
                      ),
                    ],
                  );
                },
              ),
              if (state.withdrawConfigEntity != null)
                Text(
                  '* ${'minimumAmount'.tr()} ${state.withdrawConfigEntity?.minWithdrawalAmount}, ${'maximumAmount'.tr()} ${state.withdrawConfigEntity?.maxWithdrawalAmount}',
                  style: context.textTheme.title.copyWith(
                    color: Color(0xFFE06822),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAmountTextField(BuildContext context, AlipayWithdrawState state) {
    return TextFieldWidget(
      controller: _amountController,
      hintText: 'pleaseEnterAmount'.tr(),
      textInputType: TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [NoLeadingZeroIntFormatter()],
      borderType: TextFieldBorderType.outline,
      contentPadding: EdgeInsets.symmetric(vertical: 10.gw, horizontal: 12.gw),
      borderRadius: 8.gr,
      errorText: !state.isAmountValid ? 'amount_exceeds_limit_range'.tr() : null,
    );
  }

  Widget _buildSubmitButton(BuildContext context) {
    return BlocBuilder<AlipayWithdrawCubit, AlipayWithdrawState>(
      builder: (context, state) {
        return CommonButton(
          title: 'submit'.tr(),
          enable: state.canSubmit,
          showLoading: state.submitStatus == DataStatus.loading,
          onPressed: state.canSubmit ? () => _handleSubmit(context) : null,
        );
      },
    );
  }

  Widget _buildChannelInfoText(BuildContext context) {
    return BlocSelector<AlipayWithdrawCubit, AlipayWithdrawState,
        ({String minAmount, String maxAmount, String withdrawalCount})>(
      selector: (state) => (
        minAmount: state.withdrawConfigEntity?.minWithdrawalAmount.toString() ?? '',
        maxAmount: state.withdrawConfigEntity?.maxWithdrawalAmount.toString() ?? '',
        withdrawalCount: state.withdrawConfigEntity?.withdrawalsDayCount.toString() ?? '',
      ),
      builder: (context, state) {
        return Text(
          'withdrawChannelInfo'.tr(
              namedArgs: {'min': state.minAmount, 'max': state.maxAmount, 'withdrawalCount': state.withdrawalCount}),
          style: context.textTheme.title.fs12,
          textAlign: TextAlign.center,
        );
      },
    );
  }

  void _handleSubmit(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => AlipayWithdrawPasswordDialog(
        onSubmit: (password) {
          context.read<AlipayWithdrawCubit>().submitWithdraw(password: password);
        },
      ),
    );
  }

  void _clearControllers() {
    _amountController.clear();
  }

  void _showAccountSelectionBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: false,
      backgroundColor: Colors.transparent,
      builder: (_) => BlocProvider.value(
        value: context.read<AlipayWithdrawCubit>(),
        child: BlocBuilder<AlipayWithdrawCubit, AlipayWithdrawState>(
          builder: (context, state) {
            return AccountSelectionBottomSheet(
              onAddAccount: () {
                Navigator.of(context).pop();
                _showAddAccountDialog(context);
              },
              editMode: state.isEditMode,
              list: _buildAccountList(context),
              onManageClick: () {
                context.read<AlipayWithdrawCubit>().updateEditMode();
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildAccountList(
    BuildContext context,
  ) {
    return BlocSelector<
        AlipayWithdrawCubit,
        AlipayWithdrawState,
        ({
          List<UserWalletModel> accounts,
          UserWalletModel? selectedAccount,
          bool isEditMode,
          ({int? id, DataStatus? status}) unbindWalletStatus
        })>(
      selector: (state) => (
        accounts: state.alipayAccounts,
        selectedAccount: state.selectedAlipayAccount,
        isEditMode: state.isEditMode,
        unbindWalletStatus: (id: state.unbindWalletStatus?.id, status: state.unbindWalletStatus?.status)
      ),
      builder: (context, state) => Expanded(
          child: ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 10.gw),
        itemCount: state.accounts.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: context.colorTheme.border,
          thickness: 0.5,
        ),
        itemBuilder: (context, index) {
          final account = state.accounts[index];
          final isSelected = state.selectedAccount?.id == account.id;

          return _buildAccountItem(
            context,
            account: account,
            isSelected: isSelected,
            accountName: getIt<UserCubit>().currentUser?.realName ?? '',
            onAccountSelected: (account) {
              context.read<AlipayWithdrawCubit>().updateSelectedAlipayAccount(account);
              Navigator.of(context).pop();
            },
            isEditMode: state.isEditMode,
            unbindWalletStatus: state.unbindWalletStatus,
          );
        },
      )),
    );
  }

  Widget _buildAccountItem(
    BuildContext context, {
    required UserWalletModel account,
    required bool isSelected,
    required String accountName,
    required Function(UserWalletModel?) onAccountSelected,
    required bool isEditMode,
    required ({int? id, DataStatus? status}) unbindWalletStatus,
  }) {
    final width = (1.gsw / 3) - (10.gw * 2);
    final style = context.textTheme.title;
    return InkWell(
      onTap: isEditMode ? null : () => onAccountSelected(account),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.gw),
        child: Row(
          children: [
            SizedBox(
              width: width,
              child: Text(accountName, style: style, textAlign: TextAlign.left),
            ),
            Expanded(
              child: Text(account.payAddress ?? '', style: style, textAlign: TextAlign.center),
            ),
            if (isEditMode)
              InkWell(
                onTap: () {
                  context.read<AlipayWithdrawCubit>().unbindUserWallet(account.id!);
                },
                child: unbindWalletStatus.id == account.id && unbindWalletStatus.status == DataStatus.loading
                    ? Container(
                        width: width,
                        height: 18.gw,
                        alignment: Alignment.centerRight,
                        child: SizedBox(
                          width: 18.gw,
                          height: 18.gw,
                          child: CircularProgressIndicator(
                            color: context.colorTheme.stockRed,
                            strokeWidth: 1,
                          ),
                        ),
                      )
                    : Container(
                        width: width,
                        height: 18.gw,
                        alignment: Alignment.centerRight,
                        child: Icon(Icons.delete_outlined, color: context.colorTheme.stockRed),
                      ),
              )
            else
              Container(
                width: width,
                alignment: Alignment.centerRight,
                child: Container(
                  width: 18.gw,
                  height: 18.gw,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? context.theme.primaryColor : Colors.transparent,
                    border: Border.all(
                      color: isSelected ? context.theme.primaryColor : context.colorTheme.border,
                      width: 1,
                    ),
                  ),
                  child: isSelected ? Icon(Icons.check, size: 10.gw, color: context.textTheme.secondary.color) : null,
                ),
              ),
            SizedBox(width: 15.gw),
          ],
        ),
      ),
    );
  }

  void _showAddAccountDialog(BuildContext context) {
    // 提前获取cubit引用，避免在builder中使用已销毁的context
    final cubit = context.read<AlipayWithdrawCubit>();
    final realName = getIt<UserCubit>().currentUser?.realName;
    showDialog(
      context: context,
      builder: (_) => BlocProvider.value(
        value: cubit,
        child: AddAlipayAccountDialog(
            realName: realName,
            confirmButton: (accountNumber, canConfirm) => BlocListener<AlipayWithdrawCubit, AlipayWithdrawState>(
                  listenWhen: (previous, current) => previous.bindWalletStatus != current.bindWalletStatus,
                  listener: (context, state) {
                    if (state.bindWalletStatus.isSuccess) {
                      GPEasyLoading.showToast('addedSuccessfully'.tr());
                      Navigator.of(context).pop();
                    } else if (state.bindWalletStatus.isFailed) {
                      GPEasyLoading.showToast('addFailed'.tr());
                    }
                  },
                  child: BlocSelector<AlipayWithdrawCubit, AlipayWithdrawState, bool>(
                    selector: (state) => state.bindWalletStatus.isLoading,
                    builder: (context, isLoading) {
                      return CustomMaterialButton(
                        onPressed: () => context.read<AlipayWithdrawCubit>().bindUserWallet(payAddress: accountNumber),
                        buttonText: 'confirm'.tr(),
                        color: context.theme.primaryColor,
                        borderColor: context.theme.primaryColor,
                        borderRadius: 5.gr,
                        isEnabled: canConfirm,
                        fontSize: 13.gr,
                        isLoading: isLoading,
                      );
                    },
                  ),
                )),
      ),
    );
  }
}
