import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/fund_account.dart';
import 'package:gp_stock_app/core/models/apis/third_party.dart';
import 'package:gp_stock_app/core/models/apis/withdraw.dart';
import 'package:gp_stock_app/core/models/entities/fund_account/fund_pay_way.dart';
import 'package:gp_stock_app/core/models/entities/third_party_success_response_entity.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/system_util.dart';
import 'package:gp_stock_app/features/account/domain/models/user_wallet/user_wallet_model.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel_entity_list_entity.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../shared/constants/enums.dart';

part 'third_party_channel_state.dart';

class ThirdPartyChannelCubit extends Cubit<ThirdPartyChannelState> {
  ThirdPartyChannelCubit() : super(const ThirdPartyChannelState());

  final BankRepository _bankRepository = getIt<BankRepository>();

  Future<void> getThirdPartyChannelList(int? type) async {
    emit(state.copyWith(channelListStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.list));
    final result = await ThirdPartyService.getThirdPartyChannelList(type);
    emit(state.copyWith(
      channelListStatus: DataStatus.success,
      channels: result,
      error: null,
    ));
  }

  Future<void> deposit({
    required int currentChannelIndex,
    required int selectedPaymentTypeIndex,
    required String amount,
  }) async {
    if (!validateAmount(currentChannelIndex, selectedPaymentTypeIndex, amount)) {
      return;
    }
    emit(state.copyWith(paymentStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.payment));

    if (state.updatingField == ThirdPartyChannelField.payment) {
      GPEasyLoading.showLoading(message: 'loading'.tr());
    }

    final amountValue = double.parse(amount);
    final channelId = state.channels[currentChannelIndex].payTypeList[selectedPaymentTypeIndex].payTypeId;

    final result = await ThirdPartyService.doPayin(channelId: channelId, amount: amountValue);

    if (result != null) {
      emit(state.copyWith(
        paymentStatus: DataStatus.success,
        paymentResponse: result,
        error: null,
      ));
      if (state.updatingField == ThirdPartyChannelField.payment) {
        GPEasyLoading.showToast('success'.tr());
        SystemUtil.openUrlOnSystemBrowser(url: state.paymentResponse!.payUrl, mode: LaunchMode.externalApplication);
      }
    }
  }

  void withdraw({
    required String withdrawAmount,
    required int userBankId,
    required String password,
    int? type,
    int? channelId,
  }) async {
    if (state.withdrawStatus.isLoading) return;
    emit(state.copyWith(withdrawStatus: DataStatus.loading));
    try {
      final amount = int.parse(withdrawAmount);
      final flag = await WithdrawApi.applyWithdraw(
        amount: amount,
        userBankCardId: userBankId,
        password: password.toBase64(),
        type: type,
        channelId: channelId,
      );
      emit(state.copyWith(withdrawStatus: flag ? DataStatus.success : DataStatus.idle));
      if (flag) {
        Helper.showFlutterToast('withdrawalSubmittedSuccessfully'.tr());
      }
      getIt<NavigatorService>().popUntil(AppRouter.routeWithdrawMain);
    } on FormatException {
      // 金额格式不正确
      emit(state.copyWith(error: "tips_amount_invalid_format".tr(), withdrawStatus: DataStatus.failed));
    } on Exception catch (e) {
      emit(state.copyWith(error: e.toString(), withdrawStatus: DataStatus.failed));
    }
  }

  bool validateAmount(int currentChannelIndex, int selectedPaymentTypeIndex, String amount, {bool showToast = true}) {
    if (state.channels.isEmpty) return false;
    final payType = state.channels[currentChannelIndex].payTypeList[selectedPaymentTypeIndex];
    final (amountMinLimit, amountMaxLimit) = (payType.amountMinLimit, payType.amountMaxLimit);
    final amountValue = double.tryParse(amount);
    if (amountValue == null || amountValue < amountMinLimit || amountValue > amountMaxLimit) {
      if (showToast) {
        GPEasyLoading.showToast('amountRangeError'.tr(args: [amountMinLimit.toString(), amountMaxLimit.toString()]));
      }
      return false;
    }
    return true;
  }

  // User Wallet Methods
  Future<void> getUserWalletList({String? bankCode}) async {
    if (state.walletsFetchStatus == DataStatus.loading) return;
    emit(state.copyWith(walletsFetchStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.wallets));
    final result = await _bankRepository.getUserWalletList(bankCode: bankCode);
    if (result.isSuccess && result.data != null) {
      emit(state.copyWith(
        walletsFetchStatus: DataStatus.success,
        wallets: result.data,
        error: null,
      ));
    }
  }

  Future<void> fetchSupportChannelList() async {
    final list = await FundAccountApi.fetchSupportChannelList();
    emit(state.copyWith(supportedChannels: list));
  }

  void updateSelectedWallet(UserWalletModel? wallet) {
    emit(state.copyWith(selectedWallet: () => wallet));
  }

  Future<void> bindUserWallet({
    required String bankCode,
    required String payAddress,
  }) async {
    emit(state.copyWith(bindWalletStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.bindWallet));
    final result = await ThirdPartyService.bindUserWallet(
      bankCode: bankCode,
      payAddress: payAddress,
    );

    if (result) {
      emit(state.copyWith(
        bindWalletStatus: DataStatus.success,
        error: null,
      ));
      // Refresh wallet list after successful binding
      await getUserWalletList();
    }
  }

  void updateEditMode() {
    emit(state.copyWith(isEditMode: !state.isEditMode));
  }

  Future<void> unbindUserWallet(int id) async {
    emit(state.copyWith(unbindWalletStatus: (id: id, status: DataStatus.loading)));
    try {
      final isSuccess = await ThirdPartyService.unbindUserWallet(id);

      if (isSuccess) {
        emit(state.copyWith(
          unbindWalletStatus: (id: null, status: DataStatus.success),
          selectedWallet: () => state.selectedWallet?.id == id ? null : state.selectedWallet,
        ));
        GPEasyLoading.showToast('deleteSuccess'.tr());
        await getUserWalletList();
      } else {
        emit(state.copyWith(
          unbindWalletStatus: (id: null, status: DataStatus.failed),
        ));
        GPEasyLoading.showToast('deleteFailed'.tr());
      }
    } catch (e) {
      emit(state.copyWith(
        unbindWalletStatus: (id: null, status: DataStatus.failed),
      ));
      GPEasyLoading.showToast('deleteFailedWithError'.tr(args: [e.toString()]));
    }
  }
}
