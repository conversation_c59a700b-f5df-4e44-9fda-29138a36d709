part of 'third_party_channel_cubit.dart';

enum ThirdPartyChannelField { list, payment, wallets, bindWallet }

class ThirdPartyChannelState extends Equatable {
  final DataStatus channelListStatus;
  final DataStatus paymentStatus;
  final DataStatus walletsFetchStatus;
  final DataStatus bindWalletStatus;
  final String? error;
  final ThirdPartyChannelField? updatingField;
  final List<ThirdPartyChannelEntity> channels;
  final List<FundPayWay> supportedChannels; // 可供添加的渠道类型列表
  final ThirdPartySuccessResponseEntity? paymentResponse;
  final List<UserWalletModel>? wallets;
  final UserWalletModel? selectedWallet;
  final bool isEditMode;
  final ({DataStatus? status, int? id})? unbindWalletStatus;
  final DataStatus withdrawStatus;
  const ThirdPartyChannelState({
    this.channelListStatus = DataStatus.idle,
    this.paymentStatus = DataStatus.idle,
    this.walletsFetchStatus = DataStatus.idle,
    this.bindWalletStatus = DataStatus.idle,
    this.error,
    this.updatingField,
    this.channels = const <ThirdPartyChannelEntity>[],
    this.supportedChannels = const <FundPayWay>[],
    this.paymentResponse,
    this.wallets,
    this.selectedWallet,
    this.isEditMode = false,
    this.unbindWalletStatus = (id: null, status: DataStatus.idle),
    this.withdrawStatus = DataStatus.idle,
  });

  @override
  List<Object?> get props => [
        channelListStatus,
        paymentStatus,
        walletsFetchStatus,
        bindWalletStatus,
        error,
        updatingField,
        channels,
    supportedChannels,
        paymentResponse,
        wallets,
        selectedWallet,
        isEditMode,
        unbindWalletStatus,
        withdrawStatus,
      ];

  ThirdPartyChannelState copyWith({
    DataStatus? channelListStatus,
    DataStatus? paymentStatus,
    DataStatus? walletsFetchStatus,
    DataStatus? bindWalletStatus,
    String? error,
    ThirdPartyChannelField? updatingField,
    List<ThirdPartyChannelEntity>? channels,
    List<FundPayWay>? supportedChannels,
    ThirdPartySuccessResponseEntity? paymentResponse,
    List<UserWalletModel>? wallets,
    UserWalletModel? Function()? selectedWallet,
    bool? isEditMode,
    ({DataStatus? status, int? id})? unbindWalletStatus,
    DataStatus? withdrawStatus,
  }) {
    return ThirdPartyChannelState(
      channelListStatus: channelListStatus ?? this.channelListStatus,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      walletsFetchStatus: walletsFetchStatus ?? this.walletsFetchStatus,
      bindWalletStatus: bindWalletStatus ?? this.bindWalletStatus,
      error: error ?? this.error,
      updatingField: updatingField ?? this.updatingField,
      channels: channels ?? this.channels,
      supportedChannels: supportedChannels ?? this.supportedChannels,
      paymentResponse: paymentResponse ?? this.paymentResponse,
      wallets: wallets ?? this.wallets,
      selectedWallet: selectedWallet != null ? selectedWallet() : this.selectedWallet,
      isEditMode: isEditMode ?? this.isEditMode,
      unbindWalletStatus: unbindWalletStatus ?? this.unbindWalletStatus,
      withdrawStatus: withdrawStatus ?? this.withdrawStatus,
    );
  }
}
