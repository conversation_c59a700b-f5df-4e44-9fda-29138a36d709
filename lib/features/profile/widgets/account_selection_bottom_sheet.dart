import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

class AccountSelectionBottomSheet extends StatefulWidget {
  final VoidCallback? onAddAccount;
  final Widget list;
  final bool? editMode;
  final VoidCallback? onManageClick;
  final VoidCallback? onInitState;
  final List<(String, TextAlign)> headers;

  const AccountSelectionBottomSheet({
    super.key,
    this.onAddAccount,
    required this.list,
    this.editMode,
    this.onManageClick,
    this.onInitState,
    this.headers = const [('name', TextAlign.left), ('cardNo', TextAlign.center), ('selection', TextAlign.right)],
  });

  @override
  State<AccountSelectionBottomSheet> createState() => _AccountSelectionBottomSheetState();
}

class _AccountSelectionBottomSheetState extends State<AccountSelectionBottomSheet> {
  @override
  void initState() {
    super.initState();
    widget.onInitState?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.gw),
          topRight: Radius.circular(16.gw),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          widget.list,
          if (widget.onAddAccount != null) _buildAddAccountButton(context),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.gw, horizontal: 10.gw),
      margin: EdgeInsets.symmetric(horizontal: 10.gw),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.colorTheme.border,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        spacing: 20.gw,
        children: [
          Stack(
            alignment: Alignment.centerRight,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    widget.onAddAccount != null ? 'switchWithdrawMethod'.tr() : 'switchDepositMethod'.tr(),
                    style: context.textTheme.title.copyWith(
                      fontSize: 16.gsp,
                    ),
                  ),
                ],
              ),
              if (widget.editMode != null)
                GestureDetector(
                  onTap: widget.onManageClick,
                  child: Text(widget.editMode ?? false ? 'done'.tr() : 'manage'.tr(), style: context.textTheme.primary),
                ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: widget.headers
                .map(
                  (e) => SizedBox(
                    width: (1.gsw / widget.headers.length) - (10.gw * 2),
                    child: Text(
                      e.$1.tr(),
                      style: context.textTheme.tertiary,
                      textAlign: e.$2,
                    ),
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAddAccountButton(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
        child: GestureDetector(
          onTap: widget.onAddAccount,
          child: Text(
            'addAccount'.tr(),
            style: context.textTheme.primary.copyWith(
              fontSize: 16.gsp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
