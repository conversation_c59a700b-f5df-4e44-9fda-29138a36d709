import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/market/dist_and_flow.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/quotes_cubit.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/quotes_state.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/dist_section/dist_bar_chart_v2.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/dist_section/dist_flow_chart_v2.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/quote/widgets/dist_section/dist_pie_chart_v2.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/mixin/animation.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

/// 资金分布区域 / Distribution Section
///
/// 显示资金流入流出和分布情况
/// Displays capital flow and distribution
class DistSection extends StatelessWidget with StaggeredAnimation {
  const DistSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<QuotesCubit, QuotesState, (DataStatus, DistAndFlow?)>(
      selector: (state) => (state.distFlowStatus, state.distFlow),
      builder: (context, data) {
        final status = data.$1;
        final distFlow = data.$2;

        if (status == DataStatus.loading && distFlow == null) {
          return ShimmerWidget(height: 300.gw);
        }

        if (status == DataStatus.failed || distFlow == null) {
          return const Center(child: Icon(Icons.refresh));
        }

        // 格式化更新时间 / Format update time
        final updatedTime =
            distFlow.updateTime > 0 ? DateTime.fromMillisecondsSinceEpoch(distFlow.updateTime * 1000) : null;

        return SingleChildScrollView(
          child: Column(
            spacing: 12,
            children: [
              Container(
                color: context.theme.cardColor,
                padding: const EdgeInsets.all(13.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: staggeredAnimationSlide(
                    children: [
                      Wrap(
                        spacing: 4.gw,
                        children: [
                          Text(
                            'distFlow'.tr(), // 资金成交分布
                            style: context.textTheme.primary.w700.copyWith(
                              color: context.colorTheme.textPrimary,
                            ),
                          ),
                          if (updatedTime != null)
                            Text(
                              "${'updatedAt'.tr()}： ${DateFormat('yyyy-MM-dd HH:mm').format(updatedTime)}",
                              style: const TextStyle(fontSize: 12, color: Colors.grey),
                            ),
                        ],
                      ),
                      10.verticalSpace,
                      DistPieChartV2(data: distFlow),
                      30.verticalSpace,
                      DistBarChartV2(data: distFlow),
                      30.verticalSpace,
                    ],
                  ),
                ),
              ),
              Container(
                color: context.theme.cardColor,
                padding: const EdgeInsets.all(4.0),
                child: DistFlowChartV2(data: distFlow),
              ),
              20.verticalSpace,
            ],
          ),
        );
      },
    );
  }
}
