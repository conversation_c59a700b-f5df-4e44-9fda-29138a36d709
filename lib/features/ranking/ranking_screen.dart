import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/ranking/index.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

class RankingScreen extends StatefulWidget {
  const RankingScreen({super.key});

  @override
  State<RankingScreen> createState() => _RankingScreenState();
}

class _RankingScreenState extends State<RankingScreen> {
  final tabs = ['daily', 'weekly', 'monthly'];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ranking'),
      ),
      body: BlocSelector<RankingViewCubit, RankingViewState, RankTab>(
        selector: (state) => state.tab,
        builder: (context, currentTab) {
          return Column(
            children: [
              getTabBar(
                context,
                tabs: tabs.map((e) => e.tr()).toList(),
                selectedIndex: currentTab.index,
                onTabSelected: context.read<RankingViewCubit>().updateTabIndex,
              ),
              getTabView(
                context,
                currentTab: currentTab.index,
                onPageChanged: context.read<RankingViewCubit>().updateTabIndex,
              )
            ],
          );
        },
      ),
    );
  }

  Expanded getTabView(
    BuildContext context, {
    required int currentTab,
    required Function(int) onPageChanged,
  }) {
    return Expanded(
      child: DirectSlideView(
        pages: tabs.map((e) => RankingView()).toList(),
        pageIndex: currentTab,
        onPageChanged: onPageChanged,
      ),
    );
  }

  Widget getTabBar(
    BuildContext context, {
    required List<String> tabs,
    required int selectedIndex,
    required Function(int) onTabSelected,
  }) {
    return Container(
      width: double.infinity,
      color: context.theme.appBarTheme.backgroundColor,
      padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 4.gw),
      child: CommonTabBar.withAutoKey(
        tabs,
        currentIndex: selectedIndex,
        onTap: onTabSelected,
        style: CommonTabBarStyle.line,
        isScrollable: false,
        backgroundColor: context.theme.appBarTheme.backgroundColor,
      ),
    );
  }
}
