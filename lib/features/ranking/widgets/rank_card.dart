import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/ranking/models/rank_model.dart';

class RankCard extends StatelessWidget {
  const RankCard({
    super.key,
    this.topMargin,
    required this.rankModel,
  });

  final double? topMargin;
  final RankModel rankModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10.gw, 38.gw, 10.gw, 10.gw),
      margin: EdgeInsets.only(left: 5.gw, right: 5.gw, top: topMargin ?? 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: rankModel.borderColor),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            rankModel.gradientColor,
            Colors.white,
          ],
        ),
      ),
      child: <PERSON><PERSON>(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: [
          Column(
            spacing: 6.gw,
            children: [
              Image.asset(
                'assets/icons/rank_${rankModel.rank}.png',
                height: 35.gw,
              ),
              Text('股海老王', style: context.textTheme.title.fs16.w600),
              Text('短线王者', style: context.textTheme.regular.fs12.w500),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 4.gw),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7.gr),
                  color: context.theme.primaryColor,
                ),
                child: Text('+1.05%', style: context.textTheme.secondary.fs13.w600),
              ),
              Text('\$ 100000', style: context.textTheme.tertiary.fs12.w500),
            ],
          ),
          Positioned(
            top: -70,
            child: Image.asset('assets/images/avatars/${rankModel.rank}.png', width: 50.gw, height: 50.gw),
          ),
        ],
      ),
    );
  }
}
