import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

class RankTile extends StatelessWidget {
  const RankTile({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.gw, horizontal: 8.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: context.theme.cardColor,
      ),
      child: Row(
        spacing: 12.gw,
        children: [
          Image.asset('assets/images/avatars/1.png', width: 50.gw, height: 50.gw),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 4.gw,
            children: [
              Text("股海老王", style: context.textTheme.title.fs16.w600),
              Text("短线王者", style: context.textTheme.tertiary.fs12.w500),
            ],
          ),
          Spacer(),
          Column(
            spacing: 4.gw,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 4.gw),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7.gr),
                  color: context.theme.primaryColor,
                ),
                child: Text('+1.05%', style: context.textTheme.secondary.w600),
              ),
              Text('\$ 1000', style: context.textTheme.tertiary.w500),
            ],
          ),
        ],
      ),
    );
  }
}