import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/ranking/index.dart';

class RankingView extends StatelessWidget {
  const RankingView({super.key});

  @override
  Widget build(BuildContext context) {
    final ranks = [
      RankModel(
        rank: 2,
        gradientColor: Color(0XFFFFEBD4),
        borderColor: Color(0XFFF8E9D9),
      ),
      RankModel(
        rank: 1,
        gradientColor: Color(0XFFFFD4D4),
        borderColor: Color(0XFFFFD4D4),
      ),
      RankModel(
        rank: 3,
        gradientColor: Color(0XFFFFEBAA),
        borderColor: Color(0XFFFFEBAA),
      ),
    ];
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.fromLTRB(8.gw, 20.gw, 8.gw, 20.gw),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 18.gw,
          children: [
            firstThreeRankSection(ranks),
            myRankSection(context),
            otherRankSection(context),
          ],
        ),
      ),
    );
  }

  Row firstThreeRankSection(List<RankModel> ranks) {
    return Row(
      children: ranks
          .map(
            (e) => Expanded(
              flex: e.rank == 1 ? 7 : 6,
              child: RankCard(topMargin: e.rank != 1 ? 55.gw : null, rankModel: e),
            ),
          )
          .toList(),
    );
  }

  Widget myRankSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 5.gw, right: 5.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.gw,
        children: [
          Text("我的排名（日榜）", style: context.textTheme.title.w600),
          RankTile(),
        ],
      ),
    );
  }

  Widget otherRankSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 5.gw, right: 5.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 8.gw,
        children: [
          Text("其他排名（日榜）", style: context.textTheme.title.w600),
          RankTile(),
          RankTile(),
          RankTile(),
          RankTile(),
          RankTile(),
          RankTile(),
        ],
      ),
    );
  }
}
