import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

import '../../config/flavors/app_config.dart';
import '../../generated/firebase-options/firebase_options_pre.dart' as pre;
import '../../generated/firebase-options/firebase_options_gp.dart' as gp;
import '../../generated/firebase-options/firebase_options_rsyp.dart' as rsyp;
import '../../generated/firebase-options/firebase_options_yhxt.dart' as yhxt;
import '../../generated/firebase-options/firebase_options_tempa.dart' as tempa;
import '../../generated/firebase-options/firebase_options_bszb.dart' as bszb;
import '../../generated/firebase-options/firebase_options_dyzb.dart' as dyzb;
import '../../generated/firebase-options/firebase_options_xyzq.dart' as xyzq;

/// Firebase service wrapper for managing Firebase initialization and Crashlytics
class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance => _instance ??= FirebaseService._();
  
  FirebaseService._();

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// Initialize Firebase with flavor-specific configuration
  Future<void> initialize() async {
    if (_isInitialized) {
      log('🔥 Firebase already initialized');
      return;
    }

    try {
      final flavor = AppConfig.instance.flavor;
      FirebaseOptions? options;

      // Get Firebase options based on current flavor
      switch (flavor) {
        case Flavor.pre:
          options = pre.DefaultFirebaseOptions.currentPlatform;
          break;
        case Flavor.gp:
          options = gp.DefaultFirebaseOptions.currentPlatform;
          break;
        case Flavor.rsyp:
          options = rsyp.DefaultFirebaseOptions.currentPlatform;
          break;
        case Flavor.yhxt:
          options = yhxt.DefaultFirebaseOptions.currentPlatform;
          break;
        case Flavor.tempa:
          options = tempa.DefaultFirebaseOptions.currentPlatform;
          break;
        case Flavor.bszb:
          options = bszb.DefaultFirebaseOptions.currentPlatform;
          break;
        case Flavor.dyzb:
          options = dyzb.DefaultFirebaseOptions.currentPlatform;
          break;
        case Flavor.xyzq:
          options = xyzq.DefaultFirebaseOptions.currentPlatform;
          break;
      }

      if (options != null) {
        await Firebase.initializeApp(options: options);
        await _initializeCrashlytics();
        _isInitialized = true;
        log('🔥 Firebase initialized successfully for flavor: ${flavor.name}');
      } else {
        log('⚠️ Firebase options not found for flavor: ${flavor.name}');
      }
    } catch (e, stackTrace) {
      log('❌ Failed to initialize Firebase: $e');
      log('Stack trace: $stackTrace');
      // Don't rethrow - app should continue working without Firebase
    }
  }

  /// Initialize Firebase Crashlytics
  Future<void> _initializeCrashlytics() async {
    try {
      // Pass all uncaught "fatal" errors from the framework to Crashlytics
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };

      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };

      // Enable Crashlytics collection
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

      log('🔥 Firebase Crashlytics initialized successfully');
    } catch (e) {
      log('❌ Failed to initialize Crashlytics: $e');
    }
  }

  /// Record a non-fatal error to Crashlytics
  Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    bool fatal = false,
  }) async {
    if (!_isInitialized) return;

    try {
      await FirebaseCrashlytics.instance.recordError(
        exception,
        stackTrace,
        reason: reason,
        fatal: fatal,
      );
      log('🔥 Error recorded to Crashlytics: $exception');
    } catch (e) {
      log('❌ Failed to record error to Crashlytics: $e');
    }
  }

  /// Log a message to Crashlytics
  Future<void> log(String message) async {
    if (!_isInitialized) return;

    try {
      await FirebaseCrashlytics.instance.log(message);
    } catch (e) {
      log('❌ Failed to log message to Crashlytics: $e');
    }
  }

  /// Set user identifier for Crashlytics
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) return;

    try {
      await FirebaseCrashlytics.instance.setUserIdentifier(userId);
      log('🔥 User ID set in Crashlytics: $userId');
    } catch (e) {
      log('❌ Failed to set user ID in Crashlytics: $e');
    }
  }

  /// Set custom key-value pairs for Crashlytics
  Future<void> setCustomKey(String key, dynamic value) async {
    if (!_isInitialized) return;

    try {
      await FirebaseCrashlytics.instance.setCustomKey(key, value);
    } catch (e) {
      log('❌ Failed to set custom key in Crashlytics: $e');
    }
  }

  /// Force a crash for testing purposes (debug mode only)
  void testCrash() {
    if (kDebugMode && _isInitialized) {
      FirebaseCrashlytics.instance.crash();
    }
  }
}
