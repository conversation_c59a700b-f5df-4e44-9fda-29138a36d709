import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/depth_quote.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/depth_quote.g.dart';

/// 深度行情
@JsonSerializable()
class DepthQuote {
	/// 证券市场 Market
	final String market;
	/// 证券类型 Security Type
	final String securityType;
	/// 证券代码 Symbol
	final String symbol;
	/// 收盘价 Close Price
	final double close;
	/// 最新价 Latest Price
	final double latestPrice;
	/// 卖盘 Ask Orders
	final List<DepthQuoteBidAsk> ask;
	/// 买盘 Bid Orders
	final List<DepthQuoteBidAsk> bid;

	const DepthQuote({
		this.market = '',
		this.securityType = '',
		this.symbol = '',
		this.close = 0.0,
		this.latestPrice = 0.0,
		this.ask = const [],
		this.bid = const [],
	});

	factory DepthQuote.fromJson(Map<String, dynamic> json) => $DepthQuoteFromJson(json);

	Map<String, dynamic> toJson() => $DepthQuoteToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class DepthQuoteBidAsk {
	/// 价格 Price
	final double price;
	/// 量 Volume
	final double vol;
	/// 档位 Depth Level
	final int depthNo;
	///
	final int? no;

	const DepthQuoteBidAsk({
		this.price = 0,
		this.vol = 0,
		this.depthNo = 0,
		this.no = 0,
	});

	factory DepthQuoteBidAsk.fromJson(Map<String, dynamic> json) => $DepthQuoteBidAskFromJson(json);

	Map<String, dynamic> toJson() => $DepthQuoteBidAskToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}
