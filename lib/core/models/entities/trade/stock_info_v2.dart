import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/stock_info_v2.g.dart';
import 'package:gp_stock_app/shared/models/instrument/security.dart';
import 'package:gp_stock_app/shared/models/qoute_data.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/stock_info_v2.g.dart';

/// 行情信息 Quote Information
@JsonSerializable()

class StockInfoV2 {
  /// 证券市场 Market
  final String market;

  /// 证券类型 Security Type
  final String securityType;

  /// 证券代码 Symbol
  final String symbol;

  /// 最新价 Latest Price
  final double latestPrice;

  /// 收盘价 Close Price
  final double close;

  /// 开盘价 Open Price
  final double open;

  /// 最高价 High Price
  final double high;

  /// 最低价 Low Price
  final double low;

  /// 成交量 Volume
  final int volume;

  /// 成交额 Amount
  final double amount;

  /// 每手股数 Lot Size
  final double lotSize;

  /// 保留位数 Precision
  final int precision;

  /// 币种 Currency
  final String currency;

  /// 证券名称 Security Name
  final String name;

  /// 涨跌幅 Gain
  final double gain;

  /// 涨跌额 Change
  final double chg;

  /// 最新一笔交易时间 Latest Trade Time
  final int latestTime;

  /// 证券状态 Security Status (1:正常交易 2:首日上市 3:停牌 4:除权 5:除息 6:临时停牌 7:熔断 8:待上市 9:退市)
  final int securityStatus;

  /// 市盈率TTM PE Ratio (TTM)
  final double peTtm;

  /// 市盈率静 PE Ratio (Static)
  final double peStatic;

  /// 市净率 Price-to-Book Ratio
  final double pb;

  /// 股息 Dividend
  final double dividend;

  /// 股息率 Dividend Rate
  final double dividendRate;

  /// 振幅 Amplitude
  final double amplitude;

  /// 市值 Market Value
  final double marketValue;

  /// 总股本 Total Shares
  final double totalShares;

  /// 流通股本(仅A股) Float Shares (A-Share Only)
  final String floatShares;

  /// 行业板块 Industry Plate
  final String industryPlate;

  /// 52周最高 52-Week High
  final double high52w;

  /// 52周最低 52-Week Low
  final double low52w;

  /// 市盈率动(仅港股) PE Ratio (LYR, HK Stock Only)
  final double peLyr;

  /// 换手率 Turnover Rate
  final double turnover;

  /// 竞价价格(仅美股) Bidding Price (US Stock Only)
  final double biddingPrice;

  /// 竞价涨跌幅(仅美股) Bidding Gain (US Stock Only)
  final double biddingGain;

  /// 竞价涨跌额(仅美股) Bidding Change (US Stock Only)
  final double biddingChg;

  /// 竞价最高(仅美股) Bidding High (US Stock Only)
  final double biddingHigh;

  /// 竞价最低(仅美股) Bidding Low (US Stock Only)
  final double biddingLow;

  /// 竞价成交量(仅美股) Bidding Volume (US Stock Only)
  final double biddingVolume;

  /// 竞价成交额(仅美股) Bidding Amount (US Stock Only)
  final double biddingAmount;

  /// 竞价时间(仅美股) Bidding Time (US Stock Only)
  final int biddingTime;

  /// 所属交易所(仅美股) Market Category (US Stock Only)
  final String marketCategory;

  /// 所属变动档位(仅港股) Spread Table Code (HK Stock Only)
  final String spreadTableCode;

  /// 涨停价(仅A股) Price Up Limited (A-Share Only)
  final double priceUpLimited;

  /// 跌停价(仅A股) Price Down Limited (A-Share Only)
  final double priceDownLimited;

  const StockInfoV2({
    this.market = '',
    this.securityType = '',
    this.symbol = '',
    this.latestPrice = 0.0,
    this.close = 0.0,
    this.open = 0.0,
    this.high = 0.0,
    this.low = 0.0,
    this.volume = 0,
    this.amount = 0.0,
    this.lotSize = 0.0,
    this.precision = 0,
    this.currency = '',
    this.name = '',
    this.gain = 0.0,
    this.chg = 0.0,
    this.latestTime = 0,
    this.securityStatus = 0,
    this.peTtm = 0.0,
    this.peStatic = 0.0,
    this.pb = 0.0,
    this.dividend = 0.0,
    this.dividendRate = 0.0,
    this.amplitude = 0.0,
    this.marketValue = 0.0,
    this.totalShares = 0.0,
    this.floatShares = '',
    this.industryPlate = '',
    this.high52w = 0.0,
    this.low52w = 0.0,
    this.peLyr = 0.0,
    this.turnover = 0.0,
    this.biddingPrice = 0.0,
    this.biddingGain = 0.0,
    this.biddingChg = 0.0,
    this.biddingHigh = 0.0,
    this.biddingLow = 0.0,
    this.biddingVolume = 0.0,
    this.biddingAmount = 0.0,
    this.biddingTime = 0,
    this.marketCategory = '',
    this.spreadTableCode = '',
    this.priceUpLimited = 0.0,
    this.priceDownLimited = 0.0,
  });

  factory StockInfoV2.fromJson(Map<String, dynamic> json) => $StockInfoV2FromJson(json);

  Map<String, dynamic> toJson() => $StockInfoV2ToJson(this);

  String get instrument => '$market|$securityType|$symbol';
  Security get instrumentInfo => Security(instrument: instrument, name: name);

  @override
  String toString() {
    return jsonEncode(this);
  }

  StockInfoV2 copyWith({
    String? market,
    String? securityType,
    String? symbol,
    double? latestPrice,
    double? close,
    double? open,
    double? high,
    double? low,
    int? volume,
    double? amount,
    double? lotSize,
    int? precision,
    String? currency,
    String? name,
    double? gain,
    double? chg,
    int? latestTime,
    int? securityStatus,
    double? peTtm,
    double? peStatic,
    double? pb,
    double? dividend,
    double? dividendRate,
    double? amplitude,
    double? marketValue,
    double? totalShares,
    String? floatShares,
    String? industryPlate,
    double? high52w,
    double? low52w,
    double? peLyr,
    double? turnover,
    double? biddingPrice,
    double? biddingGain,
    double? biddingChg,
    double? biddingHigh,
    double? biddingLow,
    double? biddingVolume,
    double? biddingAmount,
    int? biddingTime,
    String? marketCategory,
    String? spreadTableCode,
    double? priceUpLimited,
    double? priceDownLimited,
  }) {
    return StockInfoV2(
      market: market ?? this.market,
      securityType: securityType ?? this.securityType,
      symbol: symbol ?? this.symbol,
      latestPrice: latestPrice ?? this.latestPrice,
      close: close ?? this.close,
      open: open ?? this.open,
      high: high ?? this.high,
      low: low ?? this.low,
      volume: volume ?? this.volume,
      amount: amount ?? this.amount,
      lotSize: lotSize ?? this.lotSize,
      precision: precision ?? this.precision,
      currency: currency ?? this.currency,
      name: name ?? this.name,
      gain: gain ?? this.gain,
      chg: chg ?? this.chg,
      latestTime: latestTime ?? this.latestTime,
      securityStatus: securityStatus ?? this.securityStatus,
      peTtm: peTtm ?? this.peTtm,
      peStatic: peStatic ?? this.peStatic,
      pb: pb ?? this.pb,
      dividend: dividend ?? this.dividend,
      dividendRate: dividendRate ?? this.dividendRate,
      amplitude: amplitude ?? this.amplitude,
      marketValue: marketValue ?? this.marketValue,
      totalShares: totalShares ?? this.totalShares,
      floatShares: floatShares ?? this.floatShares,
      industryPlate: industryPlate ?? this.industryPlate,
      high52w: high52w ?? this.high52w,
      low52w: low52w ?? this.low52w,
      peLyr: peLyr ?? this.peLyr,
      turnover: turnover ?? this.turnover,
      biddingPrice: biddingPrice ?? this.biddingPrice,
      biddingGain: biddingGain ?? this.biddingGain,
      biddingChg: biddingChg ?? this.biddingChg,
      biddingHigh: biddingHigh ?? this.biddingHigh,
      biddingLow: biddingLow ?? this.biddingLow,
      biddingVolume: biddingVolume ?? this.biddingVolume,
      biddingAmount: biddingAmount ?? this.biddingAmount,
      biddingTime: biddingTime ?? this.biddingTime,
      marketCategory: marketCategory ?? this.marketCategory,
      spreadTableCode: spreadTableCode ?? this.spreadTableCode,
      priceUpLimited: priceUpLimited ?? this.priceUpLimited,
      priceDownLimited: priceDownLimited ?? this.priceDownLimited,
    );
  }

  /// 从行情数据更新 Update from Quote Data
  StockInfoV2 copyFromQuote(QuoteData quoteData) {
    return copyWith(
      chg: quoteData.chg,
      latestPrice: quoteData.latestPrice,
      high: quoteData.high,
      low: quoteData.low,
      close: quoteData.close,
      volume: quoteData.volume,
      amount: quoteData.amount,
      gain: quoteData.gain,
    );
  }
}
