# Firebase Crashlytics Setup Guide

This guide explains how to set up Firebase Crashlytics for the GP Stock app with multi-flavor support.

## Prerequisites

1. **Firebase CLI**: Install the Firebase CLI and FlutterFire CLI
   ```bash
   npm install -g firebase-tools
   dart pub global activate flutterfire_cli
   ```

2. **Firebase Projects**: Create separate Firebase projects for each flavor:
   - `gp-stock-pre` (PRE environment)
   - `gp-stock-main` (GP main environment)
   - `gp-stock-rsyp` (RSYP flavor)
   - `gp-stock-yhxt` (YHXT flavor)
   - `gp-stock-tempa` (TEMPA flavor)
   - `gp-stock-bszb` (BSZB flavor)
   - `gp-stock-dyzb` (DYZB flavor)
   - `gp-stock-xyzq` (XYZQ flavor)

3. **Firebase Authentication**: Login to Firebase CLI
   ```bash
   firebase login
   ```

## Configuration Steps

### 1. Configure Firebase for All Flavors

Run the configuration script to set up Firebase for all flavors:

```bash
# Configure all flavors at once
./scripts/firebase_configure.sh all

# Or configure individual flavors
./scripts/firebase_configure.sh gp
./scripts/firebase_configure.sh pre
./scripts/firebase_configure.sh rsyp
# ... etc
```

### 2. Enable Crashlytics in Firebase Console

For each Firebase project:

1. Go to Firebase Console → Your Project
2. Navigate to **Crashlytics** in the left sidebar
3. Click **Enable Crashlytics**
4. Follow the setup instructions

### 3. Install Dependencies

```bash
fvm flutter pub get
```

### 4. iOS Setup (if needed)

```bash
cd ios
pod install
cd ..
```

## Project Structure

After configuration, your project will have:

```
lib/generated/firebase-options/
├── firebase_options_pre.dart
├── firebase_options_gp.dart
├── firebase_options_rsyp.dart
├── firebase_options_yhxt.dart
├── firebase_options_tempa.dart
├── firebase_options_bszb.dart
├── firebase_options_dyzb.dart
└── firebase_options_xyzq.dart

android/app/src/
├── pre/google-services.json
├── gp/google-services.json
├── rsyp/google-services.json
├── yhxt/google-services.json
├── tempa/google-services.json
├── bszb/google-services.json
├── dyzb/google-services.json
└── xyzq/google-services.json

ios/Flavor/
├── pre/GoogleService-Info.plist
├── gp/GoogleService-Info.plist
├── rsyp/GoogleService-Info.plist
├── yhxt/GoogleService-Info.plist
├── tempa/GoogleService-Info.plist
├── bszb/GoogleService-Info.plist
├── dyzb/GoogleService-Info.plist
└── xyzq/GoogleService-Info.plist
```

## Usage

### Basic Usage

Firebase Crashlytics is automatically initialized when the app starts. It will:

- Capture all uncaught Flutter errors
- Capture all uncaught platform errors
- Send crash reports to the appropriate Firebase project based on the current flavor

### Manual Error Reporting

```dart
import 'package:gp_stock_app/core/services/firebase_service.dart';

// Record a non-fatal error
await FirebaseService.instance.recordError(
  exception,
  stackTrace,
  reason: 'Custom error description',
  fatal: false,
);

// Log custom messages
await FirebaseService.instance.log('User performed action X');

// Set user identifier
await FirebaseService.instance.setUserId('user123');

// Set custom key-value pairs
await FirebaseService.instance.setCustomKey('user_level', 'premium');
```

### Testing Crashlytics

In debug mode, you can force a crash to test the integration:

```dart
FirebaseService.instance.testCrash(); // Debug mode only
```

## Troubleshooting

### Common Issues

1. **Missing Firebase Options**: If you see "Firebase options not found" errors, make sure you've run the configuration script for your flavor.

2. **Build Errors**: If you encounter build errors, try:
   ```bash
   fvm flutter clean
   fvm flutter pub get
   cd ios && pod install && cd ..
   ```

3. **iOS Build Issues**: Make sure the GoogleService-Info.plist files are in the correct flavor directories.

4. **Android Build Issues**: Verify that google-services.json files are in the correct flavor source directories.

### Verification

1. **Check Firebase Console**: After running the app, crashes should appear in the Firebase Console under Crashlytics.

2. **Test with Different Flavors**: Build and run different flavors to ensure each connects to its respective Firebase project.

3. **Check Logs**: Look for Firebase initialization logs in the console output.

## Flavor-Specific Firebase Projects

Each flavor connects to its own Firebase project:

| Flavor | Firebase Project | Bundle ID (iOS) | Package Name (Android) |
|--------|------------------|-----------------|------------------------|
| pre    | gp-stock-pre     | com.gp.pre.now  | com.gp.pre.stock       |
| gp     | gp-stock-main    | com.gp.now      | com.gp.stock           |
| rsyp   | gp-stock-rsyp    | com.rsyp.stock.now | com.rsyp.stock      |
| yhxt   | gp-stock-yhxt    | com.yhxt.stock.now | com.yhxt.stock      |
| tempa  | gp-stock-tempa   | com.tempa.stock.now | com.tempa.stock    |
| bszb   | gp-stock-bszb    | com.bszb.stock.now | com.bszb.stock.now  |
| dyzb   | gp-stock-dyzb    | com.dyzb.stock.now | com.dyzb.stock      |
| xyzq   | gp-stock-xyzq    | com.xyzq.stock.now | com.xyzq.stock      |

This ensures that crash reports from different app variants are properly separated and organized.
